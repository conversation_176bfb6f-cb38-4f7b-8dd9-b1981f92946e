<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}"/>
    <title>{{ !empty($title) ? $title . ' | ' . env('APP_NAME') : env('APP_NAME') }}</title>
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <link rel="stylesheet" href="{{ asset('plugins/fontawesome-free/css/all.min.css') }}">
    <link rel="stylesheet" href="{{ asset('plugins/sweetalert2/sweetalert2.css') }}">
    <link rel="stylesheet" href="{{ asset('asset/css/adminlte.min.css') }}">
    <link rel="stylesheet" href="{{ asset('asset/css/style.css') }}">
    <!-- Modern UI Enhancements -->
    <link rel="stylesheet" href="{{ asset('asset/css/modern-enhancements.css') }}">
    <!-- Inter Font for better typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    @yield('css')
    <style>
        /* Page-specific enhancements */
        .content-wrapper {
            animation: fadeInUp 0.5s ease-out;
        }

        /* Smooth page transitions */
        .container {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Enhanced focus states for accessibility */
        *:focus {
            outline: 2px solid #4f46e5;
            outline-offset: 2px;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="hold-transition layout-top-nav">
<div class="wrapper">
    <nav class="main-header navbar navbar-expand-md navbar-light navbar-white">
        <div class="container justify-content-center">
            <a href="{{ route('home') }}" class="navbar-brand">
                <span class="brand-text font-weight-light">{{ env('APP_NAME') }}</span>
            </a>
        </div>
    </nav>
    <div class="content-wrapper">
        <div class="content {{ !empty($title) ? 'py-5' : '' }}">
            @yield('body')
        </div>
    </div>

    <!-- <footer class="main-footer text-center">
        <strong>
            Copyright &copy; {{ date('Y') }}
            {{--<a href="https://vidizayn.com">Vidizayn.com</a>.--}}
        </strong> | All rights reserved.
    </footer> -->
</div>
<script src="{{ asset('plugins/jquery/jquery.min.js') }}"></script>
<script src="{{ asset('plugins/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
<script src="{{ asset('plugins/sweetalert2/sweetalert2.all.js') }}"></script>
<script src="{{ asset('asset/js/adminlte.min.js') }}"></script>
<!-- Dark Mode Toggle Functionality -->
<script src="{{ asset('asset/js/theme-toggle.js') }}"></script>
@yield('js')
<script type="text/javascript">
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
</script>
</body>
</html>
