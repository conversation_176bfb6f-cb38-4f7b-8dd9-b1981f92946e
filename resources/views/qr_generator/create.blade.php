@extends('layout.base', ['title' => 'QR Generator'])

@section('body')
    <div class="container pb-5">
        <!-- Enhanced Header Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="text-center animate-fade-in-up">
                    <h1 class="display-4 text-gradient mb-3">
                        <i class="fas fa-magic mr-3"></i>Create QR Code
                    </h1>
                    <p class="lead text-muted mb-4">Generate beautiful, customizable QR codes in seconds</p>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center bg-transparent">
                            <li class="breadcrumb-item">
                                <a href="{{ route('qr_generator') }}" class="text-decoration-none">
                                    <i class="fas fa-home mr-1"></i>QR Manager
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">Create New</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>

        <div class="row mb-5">
            <div class="col-lg-8">
                <div class="card shadow-modern animate-slide-in-right">
                    <div class="card-header bg-gradient-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-cogs mr-2"></i>QR Code Configuration
                            </h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-light btn-sm" id="reset-form">
                                    <i class="fas fa-undo mr-1"></i>Reset
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-header p-0 pt-1 bg-white">
                        <ul class="nav nav-tabs nav-tabs-modern" id="custom-tabs-tab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="custom-tabs-text-tab" data-toggle="pill"
                                   href="#custom-tabs-text" role="tab" aria-controls="custom-tabs-text" aria-selected="true">
                                    <i class="fas fa-font mr-2"></i>Text
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="custom-tabs-link-tab" data-toggle="pill"
                                   href="#custom-tabs-link" role="tab" aria-controls="custom-tabs-link" aria-selected="false">
                                    <i class="fas fa-link mr-2"></i>Link
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="custom-tabs-vcard-tab" data-toggle="pill"
                                   href="#custom-tabs-vcard" role="tab" aria-controls="custom-tabs-vcard" aria-selected="false">
                                    <i class="fas fa-address-card mr-2"></i>vCard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="custom-tabs-wifi-tab" data-toggle="pill"
                                   href="#custom-tabs-wifi" role="tab" aria-controls="custom-tabs-wifi" aria-selected="false">
                                    <i class="fas fa-wifi mr-2"></i>WiFi
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="custom-tabs-tabContent">
                            <!-- Text QR Tab -->
                            <div class="tab-pane fade show active" id="custom-tabs-text" role="tabpanel"
                                 aria-labelledby="custom-tabs-text-tab">
                                <form data-type="text" onsubmit="return false" class="qr-form">
                                    <div class="form-group">
                                        <label for="text-input" class="form-label">
                                            <i class="fas fa-font mr-2"></i>Text Content
                                        </label>
                                        <textarea class="form-control form-control-modern" name="input" id="text-input"
                                                  placeholder="Enter the text you want to generate the QR code here..."
                                                  required rows="4" maxlength="500"></textarea>
                                        <div class="form-text">
                                            <span class="text-muted">
                                                <span id="text-char-count">0</span>/500 characters
                                            </span>
                                        </div>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary btn-lg btn-create">
                                            <i class="fas fa-magic mr-2"></i>Generate QR Code
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Link QR Tab -->
                            <div class="tab-pane fade" id="custom-tabs-link" role="tabpanel"
                                 aria-labelledby="custom-tabs-link-tab">
                                <form data-type="link" onsubmit="return false" class="qr-form">
                                    <div class="form-group">
                                        <label for="url-input" class="form-label">
                                            <i class="fas fa-link mr-2"></i>Website URL
                                        </label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">
                                                    <i class="fas fa-globe"></i>
                                                </span>
                                            </div>
                                            <input type="url" class="form-control form-control-modern" name="url" id="url-input"
                                                   placeholder="https://example.com" required>
                                        </div>
                                        <div class="form-text">
                                            <span class="text-muted">Enter a valid URL starting with http:// or https://</span>
                                        </div>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="url-preview-check">
                                            <label class="custom-control-label" for="url-preview-check">
                                                Preview website before generating QR
                                            </label>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary btn-lg btn-create">
                                            <i class="fas fa-magic mr-2"></i>Generate QR Code
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <!-- vCard QR Tab -->
                            <div class="tab-pane fade" id="custom-tabs-vcard" role="tabpanel"
                                 aria-labelledby="custom-tabs-vcard-tab">
                                <form data-type="vcard" onsubmit="return false" class="qr-form">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="first-name" class="form-label">
                                                    <i class="fas fa-user mr-2"></i>First Name *
                                                </label>
                                                <input type="text" class="form-control form-control-modern" name="first_name"
                                                       id="first-name" placeholder="John" required>
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="last-name" class="form-label">
                                                    <i class="fas fa-user mr-2"></i>Last Name *
                                                </label>
                                                <input type="text" class="form-control form-control-modern" name="last_name"
                                                       id="last-name" placeholder="Doe" required>
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="telephone" class="form-label">
                                                    <i class="fas fa-phone mr-2"></i>Phone Number
                                                </label>
                                                <input type="tel" class="form-control form-control-modern" name="telephone"
                                                       id="telephone" placeholder="+****************">
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="cellno" class="form-label">
                                                    <i class="fas fa-mobile-alt mr-2"></i>Mobile Number
                                                </label>
                                                <input type="tel" class="form-control form-control-modern" name="cellno"
                                                       id="cellno" placeholder="+****************">
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="email" class="form-label">
                                                    <i class="fas fa-envelope mr-2"></i>Email Address
                                                </label>
                                                <input type="email" class="form-control form-control-modern" name="email"
                                                       id="email" placeholder="<EMAIL>">
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="website" class="form-label">
                                                    <i class="fas fa-globe mr-2"></i>Website
                                                </label>
                                                <input type="url" class="form-control form-control-modern" name="website"
                                                       id="website" placeholder="https://johndoe.com">
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="organization" class="form-label">
                                            <i class="fas fa-building mr-2"></i>Organization
                                        </label>
                                        <input type="text" class="form-control form-control-modern" name="organization"
                                               id="organization" placeholder="Company Name">
                                    </div>

                                    <div class="form-group">
                                        <label for="fax" class="form-label">
                                            <i class="fas fa-fax mr-2"></i>Fax Number
                                        </label>
                                        <input type="tel" class="form-control form-control-modern" name="fax"
                                               id="fax" placeholder="+****************">
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary btn-lg btn-create">
                                            <i class="fas fa-magic mr-2"></i>Generate vCard QR
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <!-- WiFi QR Tab -->
                            <div class="tab-pane fade" id="custom-tabs-wifi" role="tabpanel"
                                 aria-labelledby="custom-tabs-wifi-tab">
                                <form data-type="wifi" onsubmit="return false" class="qr-form">
                                    <div class="form-group">
                                        <label for="wifi-name" class="form-label">
                                            <i class="fas fa-wifi mr-2"></i>Network Name (SSID) *
                                        </label>
                                        <input type="text" class="form-control form-control-modern" name="wifi_name"
                                               id="wifi-name" placeholder="MyWiFiNetwork" required autocomplete="off">
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="form-group">
                                        <label for="wifi-pass" class="form-label">
                                            <i class="fas fa-lock mr-2"></i>Password *
                                        </label>
                                        <div class="input-group">
                                            <input type="password" class="form-control form-control-modern" name="wifi_pass"
                                                   id="wifi-pass" placeholder="Enter WiFi password" required autocomplete="off">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary" type="button" id="toggle-wifi-password">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-shield-alt mr-2"></i>Security Type
                                        </label>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="custom-control custom-radio">
                                                    <input type="radio" class="custom-control-input" name="wifi_decode"
                                                           value="" id="wifi-none">
                                                    <label class="custom-control-label" for="wifi-none">
                                                        <i class="fas fa-unlock mr-1"></i>None
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="custom-control custom-radio">
                                                    <input type="radio" class="custom-control-input" name="wifi_decode"
                                                           value="WPA" id="wifi-wpa" checked>
                                                    <label class="custom-control-label" for="wifi-wpa">
                                                        <i class="fas fa-lock mr-1"></i>WPA/WPA2
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="custom-control custom-radio">
                                                    <input type="radio" class="custom-control-input" name="wifi_decode"
                                                           value="WEP" id="wifi-wep">
                                                    <label class="custom-control-label" for="wifi-wep">
                                                        <i class="fas fa-key mr-1"></i>WEP
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="wifi-hidden">
                                            <label class="custom-control-label" for="wifi-hidden">
                                                Hidden Network
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary btn-lg btn-create">
                                            <i class="fas fa-magic mr-2"></i>Generate WiFi QR
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Customization Panel -->
            <div class="col-lg-4">
                <div class="row">
                    <!-- Live Preview Card -->
                    <div class="col-12 mb-4">
                        <div class="card shadow-modern animate-slide-in-right" style="animation-delay: 0.2s">
                            <div class="card-header bg-gradient-info text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-eye mr-2"></i>Live Preview
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <div id="qr-preview-container" class="mb-3">
                                    <div class="qr-placeholder">
                                        <i class="fas fa-qrcode fa-4x text-muted mb-3"></i>
                                        <p class="text-muted">QR code will appear here</p>
                                    </div>
                                </div>
                                <div class="progress mb-2" style="display: none;" id="generation-progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                         role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Customization Options -->
                    <div class="col-12 mb-4">
                        <div class="card shadow-modern animate-slide-in-right" style="animation-delay: 0.3s">
                            <div class="card-header bg-gradient-warning text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-palette mr-2"></i>Customization
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Logo Upload -->
                                <div class="form-group">
                                    <label for="qr_logo" class="form-label">
                                        <i class="fas fa-image mr-2"></i>Logo/Icon
                                    </label>
                                    <div class="custom-file">
                                        <input type="file" class="custom-file-input" id="qr_logo" name="qr_logo"
                                               accept="image/png, image/jpeg, image/jpg" onchange="QrService.setLogo(this)">
                                        <label class="custom-file-label" for="qr_logo">Choose logo...</label>
                                    </div>
                                    <small class="form-text text-muted">PNG, JPG up to 2MB</small>
                                    <div class="mt-2" id="logo-preview" style="display: none;">
                                        <img id="logo-preview-img" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                        <button type="button" class="btn btn-sm btn-danger ml-2" onclick="QrService.clearLogo()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Size Settings -->
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-expand-arrows-alt mr-2"></i>Size (pixels)
                                    </label>
                                    <div class="row">
                                        <div class="col-6">
                                            <input type="number" class="form-control form-control-modern"
                                                   name="width_changer" min="100" max="2048" value="512"
                                                   onchange="QrService.updateWidth(this.value)" placeholder="Width">
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control form-control-modern"
                                                   name="height_changer" min="100" max="2048" value="512"
                                                   onchange="QrService.updateHeight(this.value)" placeholder="Height">
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="QrService.setSquare()">
                                            <i class="fas fa-square mr-1"></i>Square
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="QrService.setPreset(256)">
                                            256px
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="QrService.setPreset(512)">
                                            512px
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="QrService.setPreset(1024)">
                                            1024px
                                        </button>
                                    </div>
                                </div>

                                <!-- Color Customization -->
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-palette mr-2"></i>Colors
                                    </label>
                                    <div class="row">
                                        <div class="col-6">
                                            <label for="qr-fg-color" class="form-label-sm">Foreground</label>
                                            <input type="color" class="form-control form-control-color"
                                                   id="qr-fg-color" value="#000000" onchange="QrService.updateForegroundColor(this.value)">
                                        </div>
                                        <div class="col-6">
                                            <label for="qr-bg-color" class="form-label-sm">Background</label>
                                            <input type="color" class="form-control form-control-color"
                                                   id="qr-bg-color" value="#ffffff" onchange="QrService.updateBackgroundColor(this.value)">
                                        </div>
                                    </div>
                                </div>

                                <!-- Error Correction Level -->
                                <div class="form-group">
                                    <label for="error-correction" class="form-label">
                                        <i class="fas fa-shield-alt mr-2"></i>Error Correction
                                    </label>
                                    <select class="form-control form-control-modern" id="error-correction"
                                            onchange="QrService.updateErrorCorrection(this.value)">
                                        <option value="L">Low (7%)</option>
                                        <option value="M" selected>Medium (15%)</option>
                                        <option value="Q">Quartile (25%)</option>
                                        <option value="H">High (30%)</option>
                                    </select>
                                    <small class="form-text text-muted">Higher levels allow more damage recovery</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modal-qr-result">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Your QR is ready!</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <img class="w-100"/>
                </div>
                <div class="modal-footer justify-content-center">
                    <a href="javascript:;" class="btn btn-warning" id="download-qr" download>Download</a>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('js')
    <script>
        // Enhanced QR Service with modern features
        class EnhancedQRService {
            constructor() {
                this.formData = new FormData();
                this.width = 512;
                this.height = 512;
                this.logo = null;
                this.foregroundColor = '#000000';
                this.backgroundColor = '#ffffff';
                this.errorCorrection = 'M';
                this.previewTimeout = null;
                this.init();
            }

            init() {
                this.initEventListeners();
                this.initFormValidation();
                this.initLivePreview();
            }

            initEventListeners() {
                // Form submissions
                $('.qr-form').on('submit', (e) => {
                    e.preventDefault();
                    const type = $(e.target).data('type');
                    this.generateQR(type);
                });

                // Reset form button
                $('#reset-form').on('click', () => this.resetForm());

                // Character counter for text input
                $('#text-input').on('input', (e) => {
                    const count = e.target.value.length;
                    $('#text-char-count').text(count);
                    this.updateLivePreview();
                });

                // URL validation and preview
                $('#url-input').on('input', () => {
                    this.validateURL();
                    this.updateLivePreview();
                });

                // WiFi password toggle
                $('#toggle-wifi-password').on('click', () => this.toggleWiFiPassword());

                // Live preview updates
                $('.form-control-modern').on('input change', () => {
                    clearTimeout(this.previewTimeout);
                    this.previewTimeout = setTimeout(() => this.updateLivePreview(), 500);
                });

                // Tab change events
                $('.nav-link').on('shown.bs.tab', () => {
                    this.updateLivePreview();
                });
            }

            initFormValidation() {
                // Real-time validation for all forms
                $('.form-control-modern').on('blur', function() {
                    const field = $(this);
                    const value = field.val().trim();
                    const type = field.attr('type');
                    const required = field.prop('required');

                    field.removeClass('is-valid is-invalid');
                    field.siblings('.invalid-feedback').text('');

                    if (required && !value) {
                        field.addClass('is-invalid');
                        field.siblings('.invalid-feedback').text('This field is required.');
                        return;
                    }

                    if (value) {
                        if (type === 'email' && !qrService.isValidEmail(value)) {
                            field.addClass('is-invalid');
                            field.siblings('.invalid-feedback').text('Please enter a valid email address.');
                            return;
                        }

                        if (type === 'url' && !qrService.isValidURL(value)) {
                            field.addClass('is-invalid');
                            field.siblings('.invalid-feedback').text('Please enter a valid URL.');
                            return;
                        }

                        if (type === 'tel' && value && !qrService.isValidPhone(value)) {
                            field.addClass('is-invalid');
                            field.siblings('.invalid-feedback').text('Please enter a valid phone number.');
                            return;
                        }

                        field.addClass('is-valid');
                    }
                });
            }

            initLivePreview() {
                // Initialize the preview container
                this.updatePreviewPlaceholder();
            }

            generateQR(type) {
                if (!this.validateForm(type)) {
                    return;
                }

                const value = this.getFormValue(type);
                if (!value) {
                    this.showError('Please fill in the required fields.');
                    return;
                }

                this.showProgress();

                // Reset form data
                this.formData = new FormData();
                this.formData.append('type', type);
                this.formData.append('value', value);
                this.formData.append('width', this.width);
                this.formData.append('height', this.height);
                this.formData.append('foreground_color', this.foregroundColor);
                this.formData.append('background_color', this.backgroundColor);
                this.formData.append('error_correction', this.errorCorrection);

                if (this.logo) {
                    this.formData.append('logo', this.logo);
                }

                $.ajax({
                    url: '{{ route("qr_generator.generate") }}',
                    type: 'POST',
                    data: this.formData,
                    processData: false,
                    contentType: false,
                    success: (result) => {
                        this.hideProgress();
                        if (result.base64) {
                            this.showResult(result);
                        } else {
                            this.showError('Failed to generate QR code. Please try again.');
                        }
                    },
                    error: (xhr) => {
                        this.hideProgress();
                        const message = xhr.responseJSON?.message || 'An error occurred while generating the QR code.';
                        this.showError(message);
                    }
                });
            }

            validateForm(type) {
                const activeForm = $(`#custom-tabs-${type} .qr-form`);
                const requiredFields = activeForm.find('[required]');
                let isValid = true;

                requiredFields.each(function() {
                    const field = $(this);
                    if (!field.val().trim()) {
                        field.addClass('is-invalid');
                        field.siblings('.invalid-feedback').text('This field is required.');
                        isValid = false;
                    }
                });

                return isValid;
            }

            getFormValue(type) {
                switch (type) {
                    case 'text':
                        return $('#text-input').val().trim();
                    case 'link':
                        return $('#url-input').val().trim();
                    case 'vcard':
                        return $('#custom-tabs-vcard form').serialize();
                    case 'wifi':
                        return $('#custom-tabs-wifi form').serialize();
                    default:
                        return '';
                }
            }

            showProgress() {
                $('#generation-progress').show();
                $('.progress-bar').css('width', '0%');

                // Animate progress bar
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 30;
                    if (progress > 90) progress = 90;
                    $('.progress-bar').css('width', progress + '%');
                }, 200);

                // Store interval for cleanup
                this.progressInterval = interval;
            }

            hideProgress() {
                if (this.progressInterval) {
                    clearInterval(this.progressInterval);
                }
                $('.progress-bar').css('width', '100%');
                setTimeout(() => {
                    $('#generation-progress').hide();
                    $('.progress-bar').css('width', '0%');
                }, 500);
            }

            showResult(result) {
                $('#modal-qr-result .modal-body img').attr('src', 'data:image/png;base64,' + result.base64);
                $('#modal-qr-result .modal-footer #download-qr').attr('href', result.link);
                $('#modal-qr-result').modal('show');

                // Update live preview
                this.updatePreview('data:image/png;base64,' + result.base64);
            }

            showError(message) {
                Swal.fire({
                    title: 'Error!',
                    text: message,
                    icon: 'error',
                    confirmButtonColor: '#d33'
                });
            }

            updateLivePreview() {
                const activeTab = $('.nav-link.active').attr('href');
                const type = activeTab.replace('#custom-tabs-', '');
                const value = this.getFormValue(type);

                if (value) {
                    // Show a simple preview placeholder for now
                    this.updatePreview(null, 'Generating preview...');
                } else {
                    this.updatePreviewPlaceholder();
                }
            }

            updatePreview(imageSrc, message = null) {
                const container = $('#qr-preview-container');
                if (imageSrc) {
                    container.html(`<img src="${imageSrc}" class="img-fluid rounded shadow-sm" style="max-width: 200px;">`);
                } else if (message) {
                    container.html(`
                        <div class="qr-placeholder">
                            <div class="spinner-border text-primary mb-2" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            <p class="text-muted">${message}</p>
                        </div>
                    `);
                } else {
                    this.updatePreviewPlaceholder();
                }
            }

            updatePreviewPlaceholder() {
                $('#qr-preview-container').html(`
                    <div class="qr-placeholder">
                        <i class="fas fa-qrcode fa-4x text-muted mb-3"></i>
                        <p class="text-muted">QR code will appear here</p>
                    </div>
                `);
            }

            resetForm() {
                Swal.fire({
                    title: 'Reset Form?',
                    text: 'This will clear all form data and customizations.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, reset it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $('.qr-form')[0].reset();
                        $('.form-control').removeClass('is-valid is-invalid');
                        $('#text-char-count').text('0');
                        this.clearLogo();
                        this.updatePreviewPlaceholder();

                        // Reset to default values
                        this.width = 512;
                        this.height = 512;
                        this.foregroundColor = '#000000';
                        this.backgroundColor = '#ffffff';
                        this.errorCorrection = 'M';

                        $('[name="width_changer"]').val(512);
                        $('[name="height_changer"]').val(512);
                        $('#qr-fg-color').val('#000000');
                        $('#qr-bg-color').val('#ffffff');
                        $('#error-correction').val('M');

                        Swal.fire({
                            title: 'Reset Complete!',
                            text: 'Form has been reset to default values.',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    }
                });
            }

            toggleWiFiPassword() {
                const passwordField = $('#wifi-pass');
                const toggleButton = $('#toggle-wifi-password');
                const icon = toggleButton.find('i');

                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            }

            validateURL() {
                const urlField = $('#url-input');
                const url = urlField.val().trim();

                if (url && this.isValidURL(url)) {
                    urlField.removeClass('is-invalid').addClass('is-valid');

                    // Show preview option if enabled
                    if ($('#url-preview-check').is(':checked')) {
                        this.showURLPreview(url);
                    }
                } else if (url) {
                    urlField.removeClass('is-valid').addClass('is-invalid');
                    urlField.siblings('.invalid-feedback').text('Please enter a valid URL starting with http:// or https://');
                }
            }

            showURLPreview(url) {
                // This would show a preview of the website (implementation depends on backend support)
                console.log('URL Preview for:', url);
            }

            // Utility methods
            isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            isValidURL(url) {
                try {
                    new URL(url);
                    return url.startsWith('http://') || url.startsWith('https://');
                } catch {
                    return false;
                }
            }

            isValidPhone(phone) {
                const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
                return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
            }

            // Customization methods
            updateWidth(value) {
                this.width = parseInt(value) || 512;
                $('[name="height_changer"]').val(this.width); // Keep square by default
                this.height = this.width;
            }

            updateHeight(value) {
                this.height = parseInt(value) || 512;
            }

            updateForegroundColor(color) {
                this.foregroundColor = color;
            }

            updateBackgroundColor(color) {
                this.backgroundColor = color;
            }

            updateErrorCorrection(level) {
                this.errorCorrection = level;
            }

            setSquare() {
                const size = Math.max(this.width, this.height);
                this.width = this.height = size;
                $('[name="width_changer"], [name="height_changer"]').val(size);
            }

            setPreset(size) {
                this.width = this.height = size;
                $('[name="width_changer"], [name="height_changer"]').val(size);
            }

            setLogo(input) {
                if (input.files && input.files[0]) {
                    const file = input.files[0];

                    // Validate file size (2MB max)
                    if (file.size > 2 * 1024 * 1024) {
                        Swal.fire({
                            title: 'File Too Large',
                            text: 'Please select an image smaller than 2MB.',
                            icon: 'error'
                        });
                        input.value = '';
                        return;
                    }

                    this.logo = file;
                    $('.custom-file-label').text(file.name);

                    // Show preview
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        $('#logo-preview-img').attr('src', e.target.result);
                        $('#logo-preview').show();
                    };
                    reader.readAsDataURL(file);
                } else {
                    this.clearLogo();
                }
            }

            clearLogo() {
                this.logo = null;
                $('#qr_logo').val('');
                $('.custom-file-label').text('Choose logo...');
                $('#logo-preview').hide();
            }
        }

        // Initialize enhanced QR service
        let qrService;
        $(document).ready(function() {
            qrService = new EnhancedQRService();

            // Legacy QrService object for backward compatibility
            window.QrService = {
                setLogo: (input) => qrService.setLogo(input),
                clearLogo: () => qrService.clearLogo(),
                updateWidth: (value) => qrService.updateWidth(value),
                updateHeight: (value) => qrService.updateHeight(value),
                updateForegroundColor: (color) => qrService.updateForegroundColor(color),
                updateBackgroundColor: (color) => qrService.updateBackgroundColor(color),
                updateErrorCorrection: (level) => qrService.updateErrorCorrection(level),
                setSquare: () => qrService.setSquare(),
                setPreset: (size) => qrService.setPreset(size)
            };
        });

        // Modal event handler
        $('#modal-qr-result').on('hidden.bs.modal', function () {
            window.location.href = '{{ route("qr_generator") }}';
        });
    </script>
@endsection

@section('css')
    <style>
        /* Enhanced Create Page Styles */
        .bg-gradient-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
        }

        .bg-gradient-info {
            background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%) !important;
        }

        .bg-gradient-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%) !important;
        }

        /* Modern Navigation Tabs */
        .nav-tabs-modern {
            border-bottom: 2px solid var(--gray-200);
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
            padding: 0.5rem;
        }

        .nav-tabs-modern .nav-link {
            border: none !important;
            border-radius: var(--radius-md) !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: var(--font-weight-medium) !important;
            color: var(--gray-600) !important;
            transition: all var(--transition-fast) !important;
            margin-right: 0.25rem !important;
            background: transparent !important;
        }

        .nav-tabs-modern .nav-link:hover {
            background-color: var(--gray-100) !important;
            color: var(--gray-700) !important;
            transform: translateY(-1px);
        }

        .nav-tabs-modern .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
            color: #ffffff !important;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3) !important;
        }

        /* Enhanced Form Controls */
        .form-control-modern {
            border: 2px solid var(--border-color) !important;
            border-radius: var(--radius-md) !important;
            padding: 0.875rem 1rem !important;
            font-size: 0.9rem !important;
            transition: all var(--transition-fast) !important;
            background-color: var(--bg-primary) !important;
        }

        .form-control-modern:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
            transform: translateY(-1px);
        }

        .form-control-modern.is-valid {
            border-color: var(--success-color) !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2310b981' d='m2.3 6.73.94-.94 2.94 2.94L8.5 6.4l.94.94L6.5 10.27z'/%3e%3c/svg%3e") !important;
        }

        .form-control-modern.is-invalid {
            border-color: var(--danger-color) !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ef4444'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.5 5.5 1 1m0-1-1 1'/%3e%3c/svg%3e") !important;
        }

        /* Form Labels */
        .form-label {
            font-weight: var(--font-weight-semibold) !important;
            color: var(--text-secondary) !important;
            margin-bottom: 0.5rem !important;
            font-size: 0.875rem !important;
        }

        .form-label-sm {
            font-size: 0.75rem !important;
            font-weight: var(--font-weight-medium) !important;
            color: var(--text-muted) !important;
        }

        /* Enhanced Buttons */
        .btn-create {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
            border: none !important;
            border-radius: var(--radius-lg) !important;
            padding: 0.875rem 2rem !important;
            font-weight: var(--font-weight-semibold) !important;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3) !important;
            transition: all var(--transition-normal) !important;
        }

        .btn-create:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4) !important;
        }

        /* QR Preview Styles */
        .qr-placeholder {
            padding: 2rem;
            text-align: center;
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--bg-tertiary);
            transition: all var(--transition-fast);
        }

        .qr-placeholder:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }

        /* Color Input Styling */
        .form-control-color {
            width: 100% !important;
            height: 40px !important;
            border: 2px solid var(--border-color) !important;
            border-radius: var(--radius-md) !important;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .form-control-color:hover {
            border-color: var(--primary-color) !important;
            transform: scale(1.05);
        }

        /* Custom File Input */
        .custom-file-label {
            border: 2px solid var(--border-color) !important;
            border-radius: var(--radius-md) !important;
            padding: 0.875rem 1rem !important;
            background: var(--bg-primary) !important;
            color: var(--text-secondary) !important;
            transition: all var(--transition-fast) !important;
        }

        .custom-file-label:hover {
            border-color: var(--primary-color) !important;
            background: var(--primary-light) !important;
        }

        .custom-file-input:focus ~ .custom-file-label {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
        }

        /* Logo Preview */
        #logo-preview {
            padding: 1rem;
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            margin-top: 0.5rem;
        }

        #logo-preview-img {
            border-radius: var(--radius-sm);
            border: 2px solid var(--border-color);
        }

        /* Progress Bar Enhancement */
        .progress {
            height: 8px !important;
            border-radius: var(--radius-md) !important;
            background: var(--gray-200) !important;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary-color), var(--info-color), var(--success-color)) !important;
            background-size: 200% 100% !important;
            animation: progressGradient 2s ease infinite !important;
        }

        @keyframes progressGradient {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Custom Radio and Checkbox Styling */
        .custom-control-input:checked ~ .custom-control-label::before {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
        }

        .custom-control-input:focus ~ .custom-control-label::before {
            box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25) !important;
        }

        .custom-control-label {
            font-weight: var(--font-weight-medium) !important;
            color: var(--text-secondary) !important;
        }

        /* Input Group Enhancements */
        .input-group-text {
            background: var(--bg-tertiary) !important;
            border: 2px solid var(--border-color) !important;
            color: var(--text-muted) !important;
            font-weight: var(--font-weight-medium) !important;
        }

        /* Character Counter */
        .form-text {
            font-size: 0.75rem !important;
            margin-top: 0.25rem !important;
        }

        /* Breadcrumb Enhancement */
        .breadcrumb {
            background: transparent !important;
            padding: 0 !important;
        }

        .breadcrumb-item a {
            color: var(--primary-color) !important;
            text-decoration: none !important;
            font-weight: var(--font-weight-medium) !important;
            transition: all var(--transition-fast) !important;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-hover) !important;
            transform: translateX(2px);
        }

        /* Animation Classes */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        .animate-slide-in-right {
            animation: slideInRight 0.8s ease-out;
        }

        /* Responsive Improvements */
        @media (max-width: 768px) {
            .nav-tabs-modern .nav-link {
                padding: 0.5rem 1rem !important;
                font-size: 0.875rem !important;
            }

            .btn-create {
                padding: 0.75rem 1.5rem !important;
                font-size: 0.875rem !important;
            }

            .form-control-modern {
                padding: 0.75rem !important;
            }

            .qr-placeholder {
                padding: 1.5rem !important;
            }
        }

        /* Dark Mode Specific Enhancements */
        [data-theme="dark"] .nav-tabs-modern {
            background: var(--bg-tertiary);
        }

        [data-theme="dark"] .nav-tabs-modern .nav-link:hover {
            background-color: var(--bg-secondary) !important;
            color: var(--text-primary) !important;
        }

        [data-theme="dark"] .qr-placeholder {
            background: var(--bg-secondary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .qr-placeholder:hover {
            background: var(--primary-light);
        }

        [data-theme="dark"] .custom-file-label {
            background: var(--bg-primary) !important;
            color: var(--text-secondary) !important;
        }

        [data-theme="dark"] .input-group-text {
            background: var(--bg-tertiary) !important;
            color: var(--text-secondary) !important;
        }

        [data-theme="dark"] #logo-preview {
            background: var(--bg-secondary);
            border-color: var(--border-color);
        }
    </style>
@endsection
