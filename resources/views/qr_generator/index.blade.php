@extends('layout.base', ['title' => 'QR Generator'])

@section('body')
    <div class="container pb-5">
        <!-- Enhanced Header Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="text-center animate-fade-in-up">
                    <h1 class="display-4 text-gradient mb-3">
                        <i class="fas fa-qrcode mr-3"></i>QR Code Manager
                    </h1>
                    <p class="lead text-muted mb-4">Create, manage, and track your QR codes with ease</p>
                </div>
            </div>
        </div>

        <!-- Statistics Dashboard -->
        <div class="row mb-5">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card bg-gradient-primary text-white shadow-modern animate-slide-in-right" style="animation-delay: 0.1s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">Total QR Codes</h5>
                                <h2 class="mb-0">{{ count($qr_list) }}</h2>
                            </div>
                            <div class="ml-3">
                                <i class="fas fa-qrcode fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card bg-gradient-success text-white shadow-modern animate-slide-in-right" style="animation-delay: 0.2s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">Links</h5>
                                <h2 class="mb-0">{{ $qr_list->where('type', 'link')->count() }}</h2>
                            </div>
                            <div class="ml-3">
                                <i class="fas fa-link fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card bg-gradient-info text-white shadow-modern animate-slide-in-right" style="animation-delay: 0.3s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">Text Codes</h5>
                                <h2 class="mb-0">{{ $qr_list->where('type', 'text')->count() }}</h2>
                            </div>
                            <div class="ml-3">
                                <i class="fas fa-font fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card bg-gradient-warning text-white shadow-modern animate-slide-in-right" style="animation-delay: 0.4s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">vCards</h5>
                                <h2 class="mb-0">{{ $qr_list->where('type', 'vcard')->count() }}</h2>
                            </div>
                            <div class="ml-3">
                                <i class="fas fa-address-card fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced QR List Card -->
        <div class="card shadow-modern animate-fade-in-up" style="animation-delay: 0.5s">
            <div class="card-header bg-gradient-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-list mr-2"></i>QR Code Collection
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-light btn-sm" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Advanced Search and Filter Section -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                            </div>
                            <input type="text" class="form-control" id="advanced-search" placeholder="Search QR codes...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-control" id="type-filter">
                            <option value="">All Types</option>
                            <option value="text">Text</option>
                            <option value="link">Link</option>
                            <option value="vcard">vCard</option>
                            <option value="wifi">WiFi</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-outline-secondary" id="grid-view">
                                <i class="fas fa-th"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary active" id="list-view">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Bulk Actions Bar -->
                <div class="row mb-3" id="bulk-actions" style="display: none;">
                    <div class="col-12">
                        <div class="alert alert-info d-flex justify-content-between align-items-center">
                            <span><strong id="selected-count">0</strong> items selected</span>
                            <div>
                                <button class="btn btn-sm btn-danger" id="bulk-delete">
                                    <i class="fas fa-trash mr-1"></i>Delete Selected
                                </button>
                                <button class="btn btn-sm btn-info" id="bulk-export">
                                    <i class="fas fa-download mr-1"></i>Export Selected
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <table class="table table-hover" id="qr-list">
                    <thead>
                    <tr>
                        <th width="40">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="select-all">
                                <label class="custom-control-label" for="select-all"></label>
                            </div>
                        </th>
                        <th>Preview</th>
                        <th>Hash Code</th>
                        <th>Type</th>
                        <th>Target Content</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($qr_list as $item)
                        <tr data-type="{{ $item->type }}" data-id="{{ $item->id }}">
                            <td>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input row-checkbox" id="check-{{ $item->id }}">
                                    <label class="custom-control-label" for="check-{{ $item->id }}"></label>
                                </div>
                            </td>
                            <td>
                                <div class="qr-preview-container" data-qr-url="{{ $item->url }}">
                                    <div class="qr-preview-placeholder">
                                        <i class="fas fa-qrcode fa-2x text-muted"></i>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <code class="bg-light px-2 py-1 rounded">{{ $item->hash_code }}</code>
                            </td>
                            <td>
                                <span class="badge badge-{{ $item->type == 'link' ? 'primary' : ($item->type == 'text' ? 'info' : ($item->type == 'vcard' ? 'warning' : 'success')) }}">
                                    <i class="fas fa-{{ $item->type == 'link' ? 'link' : ($item->type == 'text' ? 'font' : ($item->type == 'vcard' ? 'address-card' : 'wifi')) }} mr-1"></i>
                                    {{ ucfirst($item->type) }}
                                </span>
                            </td>
                            <td>
                                <div class="content-preview" title="{{ $item->target }}">
                                    {{ Str::limit($item->target, 40) }}
                                </div>
                            </td>
                            <td>
                                <span class="text-muted" title="{{ $item->created_at }}">
                                    {{ $item->created_at->diffForHumans() }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ $item->url }}" target="_blank" class="btn btn-primary btn-sm" title="View QR Code">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if($item->type == 'link')
                                        <button class="btn btn-warning btn-sm" data-toggle="modal"
                                               data-target="#modal-edit-target"
                                               onclick="setEdit('{{ $item->id }}', '{{ $item->target }}')" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    @endif
                                    <button class="btn btn-info btn-sm qr-download" data-url="{{ $item->url }}" title="Download">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-danger btn-sm qr-delete" data-id="{{ $item->id }}" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modal-edit-target">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('qr_generator.edit') }}" method="POST">
                    @csrf
                    <div class="modal-header">
                        <h4 class="modal-title">Edit Target</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="input_old_target">Old Target</label>
                            <input type="url" class="form-control" id="input_old_target" value="" disabled>
                        </div>
                        <div class="form-group">
                            <label for="input_new_target">New Target <span class="text-red">*</span></label>
                            <input type="url" class="form-control" id="input_new_target" name="new_target"
                                   placeholder="This input is required*" required>
                        </div>
                    </div>
                    <div class="modal-footer justify-content-center">
                        <input type="hidden" id="input_id" name="id" value="">
                        <button type="submit" class="btn btn-success">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
@section('js')
    <script src="{{ asset('plugins/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('plugins/datatables-responsive/js/dataTables.responsive.min.js') }}"></script>
    <script src="{{ asset('plugins/datatables-responsive/js/responsive.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('plugins/datatables-buttons/js/dataTables.buttons.min.js') }}"></script>
    <script src="{{ asset('plugins/datatables-buttons/js/buttons.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('plugins/jszip/jszip.min.js') }}"></script>
    <script src="{{ asset('plugins/pdfmake/pdfmake.min.js') }}"></script>
    <script src="{{ asset('plugins/pdfmake/vfs_fonts.js') }}"></script>
    <script src="{{ asset('plugins/datatables-buttons/js/buttons.html5.min.js') }}"></script>
    <script src="{{ asset('plugins/datatables-buttons/js/buttons.print.min.js') }}"></script>
    <script src="{{ asset('plugins/datatables-buttons/js/buttons.colVis.min.js') }}"></script>
    <script>
        // Enhanced QR Manager Class
        class QRManager {
            constructor() {
                this.dataTable = null;
                this.selectedRows = new Set();
                this.init();
            }

            init() {
                this.initDataTable();
                this.initEventListeners();
                this.initQRPreviews();
                this.initAnimations();
            }

            initDataTable() {
                this.dataTable = $("#qr-list").DataTable({
                    "order": [[3, 'desc']],
                    "dom": '<"d-flex justify-content-between align-items-center mb-3"<"d-flex"B><"ml-auto"f>>rt<"d-flex justify-content-between align-items-center mt-3"<"d-flex align-items-center"li><"ml-auto"p>>',
                    "responsive": true,
                    "lengthChange": true,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "pageLength": 10,
                    "autoWidth": false,
                    "searching": false, // We'll use custom search
                    "columnDefs": [
                        { "orderable": false, "targets": [0, 1, 6] }, // Checkbox, preview, and actions columns
                        { "searchable": false, "targets": [0, 1, 6] }
                    ],
                    "buttons": [
                        {
                            text: '<i class="fas fa-plus mr-1"></i>Create New QR',
                            className: 'btn btn-success btn-sm shadow-sm',
                            action: function () {
                                window.location.href = '{{ route("qr_generator.create") }}';
                            }
                        },
                        {
                            extend: 'csv',
                            text: '<i class="fas fa-file-csv mr-1"></i>CSV',
                            className: 'btn btn-info btn-sm shadow-sm'
                        },
                        {
                            extend: 'excelHtml5',
                            text: '<i class="fas fa-file-excel mr-1"></i>Excel',
                            className: 'btn btn-info btn-sm shadow-sm'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: '<i class="fas fa-file-pdf mr-1"></i>PDF',
                            className: 'btn btn-info btn-sm shadow-sm'
                        },
                        {
                            extend: 'print',
                            text: '<i class="fas fa-print mr-1"></i>Print',
                            className: 'btn btn-info btn-sm shadow-sm'
                        }
                    ],
                    "language": {
                        "emptyTable": "No QR codes found. <a href='{{ route("qr_generator.create") }}' class='btn btn-primary btn-sm ml-2'>Create your first QR code</a>",
                        "zeroRecords": "No matching QR codes found.",
                        "info": "Showing _START_ to _END_ of _TOTAL_ QR codes",
                        "infoEmpty": "Showing 0 to 0 of 0 QR codes",
                        "infoFiltered": "(filtered from _MAX_ total QR codes)"
                    },
                    "drawCallback": function() {
                        // Re-initialize tooltips and other features after table redraw
                        $('[data-toggle="tooltip"]').tooltip();
                        qrManager.updateQRPreviews();
                    }
                });
            }

            initEventListeners() {
                // Advanced search functionality
                $('#advanced-search').on('keyup', (e) => {
                    this.performAdvancedSearch(e.target.value);
                });

                // Type filter
                $('#type-filter').on('change', (e) => {
                    this.filterByType(e.target.value);
                });

                // View toggle
                $('#grid-view, #list-view').on('click', (e) => {
                    this.toggleView(e.currentTarget.id);
                });

                // Select all checkbox
                $('#select-all').on('change', (e) => {
                    this.toggleSelectAll(e.target.checked);
                });

                // Individual row checkboxes
                $(document).on('change', '.row-checkbox', (e) => {
                    this.toggleRowSelection(e.target);
                });

                // Bulk actions
                $('#bulk-delete').on('click', () => this.bulkDelete());
                $('#bulk-export').on('click', () => this.bulkExport());

                // Individual actions
                $(document).on('click', '.qr-download', (e) => {
                    this.downloadQR(e.currentTarget.dataset.url);
                });

                $(document).on('click', '.qr-delete', (e) => {
                    this.deleteQR(e.currentTarget.dataset.id);
                });

                // QR preview hover
                $(document).on('mouseenter', '.qr-preview-container', (e) => {
                    this.showQRPreview(e.currentTarget);
                });

                $(document).on('mouseleave', '.qr-preview-container', (e) => {
                    this.hideQRPreview(e.currentTarget);
                });
            }

            performAdvancedSearch(searchTerm) {
                const rows = $('#qr-list tbody tr');

                if (!searchTerm) {
                    rows.show();
                    this.dataTable.draw();
                    return;
                }

                rows.each(function() {
                    const row = $(this);
                    const text = row.text().toLowerCase();
                    const hashCode = row.find('code').text().toLowerCase();
                    const content = row.find('.content-preview').text().toLowerCase();

                    if (text.includes(searchTerm.toLowerCase()) ||
                        hashCode.includes(searchTerm.toLowerCase()) ||
                        content.includes(searchTerm.toLowerCase())) {
                        row.show();
                    } else {
                        row.hide();
                    }
                });
            }

            filterByType(type) {
                if (!type) {
                    $('#qr-list tbody tr').show();
                } else {
                    $('#qr-list tbody tr').each(function() {
                        const row = $(this);
                        if (row.data('type') === type) {
                            row.show();
                        } else {
                            row.hide();
                        }
                    });
                }
                this.dataTable.draw();
            }

            toggleView(viewType) {
                $('#grid-view, #list-view').removeClass('active');
                $(`#${viewType}`).addClass('active');

                if (viewType === 'grid-view') {
                    this.switchToGridView();
                } else {
                    this.switchToListView();
                }
            }

            switchToGridView() {
                // Implementation for grid view (could be added later)
                Swal.fire({
                    title: 'Grid View',
                    text: 'Grid view coming soon!',
                    icon: 'info',
                    timer: 2000,
                    showConfirmButton: false
                });
            }

            switchToListView() {
                // Already in list view
            }

            toggleSelectAll(checked) {
                $('.row-checkbox').prop('checked', checked);
                if (checked) {
                    $('.row-checkbox').each((i, checkbox) => {
                        this.selectedRows.add(checkbox.id);
                    });
                } else {
                    this.selectedRows.clear();
                }
                this.updateBulkActions();
            }

            toggleRowSelection(checkbox) {
                if (checkbox.checked) {
                    this.selectedRows.add(checkbox.id);
                } else {
                    this.selectedRows.delete(checkbox.id);
                }
                this.updateBulkActions();

                // Update select all checkbox
                const totalCheckboxes = $('.row-checkbox').length;
                const checkedCheckboxes = $('.row-checkbox:checked').length;
                $('#select-all').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
                $('#select-all').prop('checked', checkedCheckboxes === totalCheckboxes);
            }

            updateBulkActions() {
                const selectedCount = this.selectedRows.size;
                $('#selected-count').text(selectedCount);

                if (selectedCount > 0) {
                    $('#bulk-actions').slideDown();
                } else {
                    $('#bulk-actions').slideUp();
                }
            }

            bulkDelete() {
                if (this.selectedRows.size === 0) return;

                Swal.fire({
                    title: 'Delete Selected QR Codes?',
                    text: `Are you sure you want to delete ${this.selectedRows.size} QR code(s)? This action cannot be undone.`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, delete them!',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Here you would implement the actual deletion logic
                        // For now, we'll just show a success message
                        Swal.fire({
                            title: 'Deleted!',
                            text: 'Selected QR codes have been deleted.',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });

                        // Clear selections
                        this.selectedRows.clear();
                        this.updateBulkActions();
                        $('#select-all').prop('checked', false);
                        $('.row-checkbox').prop('checked', false);
                    }
                });
            }

            bulkExport() {
                if (this.selectedRows.size === 0) return;

                Swal.fire({
                    title: 'Export Selected QR Codes',
                    text: `Exporting ${this.selectedRows.size} QR code(s)...`,
                    icon: 'info',
                    timer: 2000,
                    showConfirmButton: false
                });

                // Here you would implement the actual export logic
            }

            downloadQR(url) {
                // Create a temporary link to download the QR code
                const link = document.createElement('a');
                link.href = url;
                link.download = `qr-code-${Date.now()}.png`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success message
                Swal.fire({
                    title: 'Download Started',
                    text: 'QR code download has started.',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            }

            deleteQR(id) {
                Swal.fire({
                    title: 'Delete QR Code?',
                    text: 'Are you sure you want to delete this QR code? This action cannot be undone.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, delete it!',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Here you would implement the actual deletion logic
                        // For now, we'll just show a success message
                        Swal.fire({
                            title: 'Deleted!',
                            text: 'QR code has been deleted.',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    }
                });
            }

            showQRPreview(container) {
                const qrUrl = container.dataset.qrUrl;
                if (!qrUrl) return;

                // Create preview image
                const img = document.createElement('img');
                img.src = qrUrl;
                img.className = 'qr-preview-image';
                img.style.cssText = `
                    position: absolute;
                    top: -10px;
                    left: 50px;
                    width: 150px;
                    height: 150px;
                    border: 2px solid var(--primary-color);
                    border-radius: 8px;
                    background: white;
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                    z-index: 1000;
                    transform: scale(0);
                    transition: transform 0.2s ease-out;
                `;

                container.style.position = 'relative';
                container.appendChild(img);

                // Animate in
                setTimeout(() => {
                    img.style.transform = 'scale(1)';
                }, 10);
            }

            hideQRPreview(container) {
                const preview = container.querySelector('.qr-preview-image');
                if (preview) {
                    preview.style.transform = 'scale(0)';
                    setTimeout(() => {
                        if (preview.parentNode) {
                            preview.parentNode.removeChild(preview);
                        }
                    }, 200);
                }
            }

            initQRPreviews() {
                // Initialize QR preview placeholders with hover effects
                $('.qr-preview-container').each(function() {
                    $(this).css({
                        'width': '40px',
                        'height': '40px',
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'center',
                        'border': '2px dashed var(--border-color)',
                        'border-radius': '8px',
                        'cursor': 'pointer',
                        'transition': 'all 0.2s ease'
                    });
                });
            }

            updateQRPreviews() {
                this.initQRPreviews();
            }

            initAnimations() {
                // Add staggered animations to table rows
                $('#qr-list tbody tr').each(function(index) {
                    $(this).css({
                        'animation': `fadeInUp 0.5s ease-out ${index * 0.05}s both`
                    });
                });

                // Add hover effects to action buttons
                $(document).on('mouseenter', '.btn-group .btn', function() {
                    $(this).addClass('shadow-sm');
                }).on('mouseleave', '.btn-group .btn', function() {
                    $(this).removeClass('shadow-sm');
                });
            }
        }

        // Initialize QR Manager
        let qrManager;
        $(document).ready(function() {
            qrManager = new QRManager();
        });

        // Legacy function for edit modal (keeping for compatibility)
        function setEdit(id, old_value) {
            let modal = $('#modal-edit-target');
            modal.find('#input_old_target').val(old_value);
            modal.find('#input_id').val(id);
        }
    </script>
@endsection
@section('css')
    <link rel="stylesheet" href="{{ asset('plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') }}">
    <link rel="stylesheet" href="{{ asset('plugins/datatables-responsive/css/responsive.bootstrap4.min.css') }}">
    <link rel="stylesheet" href="{{ asset('plugins/datatables-buttons/css/buttons.bootstrap4.min.css') }}">
    <style>
        /* Enhanced QR Generator Styles */
        .bg-gradient-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
        }

        .bg-gradient-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%) !important;
        }

        .bg-gradient-info {
            background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%) !important;
        }

        .bg-gradient-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%) !important;
        }

        /* Statistics Cards */
        .card.bg-gradient-primary,
        .card.bg-gradient-success,
        .card.bg-gradient-info,
        .card.bg-gradient-warning {
            border: none !important;
            color: white !important;
            overflow: hidden;
            position: relative;
        }

        .card.bg-gradient-primary::before,
        .card.bg-gradient-success::before,
        .card.bg-gradient-info::before,
        .card.bg-gradient-warning::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        /* Enhanced DataTable Styling */
        .dt-buttons {
            margin-bottom: 1rem;
        }

        .dt-buttons .btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_filter {
            display: none; /* We're using custom search */
        }

        .dataTables_wrapper .dataTables_length select {
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 0.375rem 0.75rem;
        }

        .dataTables_wrapper .dataTables_info {
            color: var(--text-muted);
            font-weight: var(--font-weight-medium);
        }

        /* QR Preview Styles */
        .qr-preview-container {
            position: relative;
            transition: all 0.2s ease;
        }

        .qr-preview-container:hover {
            border-color: var(--primary-color) !important;
            background-color: var(--primary-light) !important;
        }

        .qr-preview-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qr-preview-image {
            pointer-events: none;
        }

        /* Content Preview */
        .content-preview {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: help;
        }

        /* Badge Enhancements */
        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-md);
        }

        /* Action Button Groups */
        .btn-group .btn {
            border-radius: var(--radius-sm) !important;
            margin-right: 2px;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }

        /* Bulk Actions Bar */
        #bulk-actions .alert {
            border: none;
            border-radius: var(--radius-lg);
            background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
            border-left: 4px solid var(--info-color);
        }

        /* Custom Checkbox Styling */
        .custom-control-input:checked ~ .custom-control-label::before {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .custom-control-input:focus ~ .custom-control-label::before {
            box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
        }

        /* Search and Filter Enhancements */
        .input-group-text {
            background-color: var(--bg-tertiary);
            border-color: var(--border-color);
            color: var(--text-muted);
        }

        /* View Toggle Buttons */
        .btn-group .btn.active {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            color: white !important;
        }

        /* Table Row Hover Effects */
        #qr-list tbody tr {
            transition: all 0.2s ease;
        }

        #qr-list tbody tr:hover {
            background-color: var(--bg-tertiary) !important;
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Code Styling */
        code {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.8rem;
            border-radius: var(--radius-sm);
        }

        /* Animation Classes */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .animate-slide-in-right {
            animation: slideInRight 0.6s ease-out;
        }

        /* Responsive Improvements */
        @media (max-width: 768px) {
            .card-body {
                padding: 1rem;
            }

            .btn-group .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }

            .content-preview {
                max-width: 120px;
            }

            .qr-preview-container {
                width: 30px !important;
                height: 30px !important;
            }
        }

        /* Dark Mode Specific Enhancements */
        [data-theme="dark"] .bg-gradient-primary::before,
        [data-theme="dark"] .bg-gradient-success::before,
        [data-theme="dark"] .bg-gradient-info::before,
        [data-theme="dark"] .bg-gradient-warning::before {
            background: rgba(255, 255, 255, 0.05);
        }

        [data-theme="dark"] #bulk-actions .alert {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
            color: #bfdbfe;
        }

        [data-theme="dark"] .input-group-text {
            background-color: var(--bg-tertiary);
            color: var(--text-secondary);
        }

        [data-theme="dark"] code {
            background-color: var(--bg-tertiary) !important;
            color: var(--text-primary) !important;
        }
    </style>
@endsection
