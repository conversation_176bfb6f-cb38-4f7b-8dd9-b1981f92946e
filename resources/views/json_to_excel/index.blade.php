@extends('layout.base', ['title' => 'JSON to Excel Converter'])

@section('body')
    <div class="container pb-5 animate-fade-in-up">
        <!-- Header Section -->
        <div class="text-center mb-5">
            <h1 class="text-gradient mb-3">
                <i class="fas fa-code text-info me-3"></i>
                JSON to Excel Converter
            </h1>
            <p class="lead text-muted">Transform your JSON data into Excel spreadsheets effortlessly</p>
        </div>

        <!-- Input Section -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow-modern mb-5" id="input-card">
                    <div class="card-header bg-gradient-info text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i>
                            Input Your JSON Data
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- Input Method Tabs -->
                        <ul class="nav nav-tabs mb-4" id="inputTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="paste-tab" data-bs-toggle="tab" data-bs-target="#paste-content" type="button" role="tab">
                                    <i class="fas fa-paste me-2"></i>Paste JSON
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-content" type="button" role="tab">
                                    <i class="fas fa-upload me-2"></i>Upload File
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="inputTabContent">
                            <!-- Paste JSON Tab -->
                            <div class="tab-pane fade show active" id="paste-content" role="tabpanel">
                                <div class="form-group">
                                    <label for="json_data" class="form-label">
                                        <i class="fas fa-code me-2"></i>
                                        Paste your JSON data below
                                    </label>
                                    <div class="json-input-container">
                                        <textarea class="form-control json-textarea"
                                                  rows="12"
                                                  id="json_data"
                                                  placeholder='Paste your JSON here, for example:
[
  {
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": 30
  },
  {
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "age": 25
  }
]'></textarea>
                                        <div class="json-input-actions">
                                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="formatInputJSON()">
                                                <i class="fas fa-indent me-1"></i>Format
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="validateJSON()">
                                                <i class="fas fa-check me-1"></i>Validate
                                            </button>
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearJSON()">
                                                <i class="fas fa-trash me-1"></i>Clear
                                            </button>
                                        </div>
                                    </div>
                                    <div class="json-status" id="json-status"></div>
                                </div>
                            </div>

                            <!-- Upload File Tab -->
                            <div class="tab-pane fade" id="upload-content" role="tabpanel">
                                <!-- Drag & Drop Zone for JSON files -->
                                <div class="upload-zone" id="json-upload-zone">
                                    <div class="upload-zone-content">
                                        <i class="fas fa-file-code upload-icon"></i>
                                        <h4>Drag & Drop your JSON file here</h4>
                                        <p class="text-muted">or click to browse files</p>
                                        <div class="supported-formats">
                                            <span class="badge badge-info me-2">.json</span>
                                            <span class="badge badge-info">.txt</span>
                                        </div>
                                    </div>
                                    <input type="file" class="file-input" id="json_file_upload"
                                           accept=".json,.txt,application/json,text/plain">
                                </div>

                                <!-- File Info Display -->
                                <div class="file-info" id="json-file-info" style="display: none;">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-code text-info me-3 fs-2"></i>
                                        <div class="flex-grow-1">
                                            <h5 class="mb-1" id="json-file-name"></h5>
                                            <small class="text-muted" id="json-file-size"></small>
                                        </div>
                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearJSONFile()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="progress-container" id="progress-container" style="display: none;">
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     id="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="text-center">
                                <small class="text-muted" id="progress-text">Processing...</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="conversion-options">
                                    <label class="form-label">
                                        <i class="fas fa-cog me-2"></i>
                                        Export Options
                                    </label>
                                    <div class="d-flex gap-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="exportFormat" id="xlsx" value="xlsx" checked>
                                            <label class="form-check-label" for="xlsx">
                                                <i class="fas fa-file-excel text-success me-1"></i>XLSX
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="exportFormat" id="csv" value="csv">
                                            <label class="form-check-label" for="csv">
                                                <i class="fas fa-file-csv text-warning me-1"></i>CSV
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <button type="button" class="btn btn-info btn-lg" id="convert-btn" onclick="exportData()" disabled>
                                    <i class="fas fa-file-export me-2"></i>
                                    Convert to Excel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Section -->
        <div class="row" id="preview-section" style="display: none;">
            <div class="col-12">
                <div class="card shadow-modern">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-table text-success me-2"></i>
                            Data Preview
                        </h3>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="downloadExcel()">
                                <i class="fas fa-download me-1"></i>
                                Download
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="previewMore()">
                                <i class="fas fa-eye me-1"></i>
                                View All
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="preview-table">
                                <thead class="table-light">
                                    <!-- Headers will be populated dynamically -->
                                </thead>
                                <tbody>
                                    <!-- Data will be populated dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <small class="text-muted">
                                    <i class="fas fa-list me-1"></i>
                                    Records: <span id="preview-record-count">0</span>
                                </small>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">
                                    <i class="fas fa-columns me-1"></i>
                                    Columns: <span id="preview-column-count">0</span>
                                </small>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">
                                    <i class="fas fa-file me-1"></i>
                                    Format: <span id="preview-format">XLSX</span>
                                </small>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    Generated: <span id="preview-timestamp">Now</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.min.js"></script>
    <script>
        // Global variables
        let jsonData = null;
        let currentFile = null;
        let isProcessing = false;
        let previewData = null;

        // Initialize when DOM is ready
        $(document).ready(function() {
            initializeComponents();
            initializeEventListeners();
        });

        function initializeComponents() {
            initializeUploadZone();
            initializeTooltips();
            initializeTabs();
        }

        function initializeUploadZone() {
            const uploadZone = document.getElementById('json-upload-zone');
            const fileInput = document.getElementById('json_file_upload');

            if (uploadZone && fileInput) {
                // Drag and drop events
                uploadZone.addEventListener('dragover', handleDragOver);
                uploadZone.addEventListener('dragleave', handleDragLeave);
                uploadZone.addEventListener('drop', handleJSONDrop);
                uploadZone.addEventListener('click', () => fileInput.click());

                // File input change event
                fileInput.addEventListener('change', handleJSONFileSelect);
            }
        }

        function initializeTooltips() {
            // Initialize Bootstrap tooltips if available
            if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }
        }

        function initializeTabs() {
            // Initialize Bootstrap tabs if available
            if (typeof bootstrap !== 'undefined' && bootstrap.Tab) {
                var triggerTabList = [].slice.call(document.querySelectorAll('#inputTabs button'));
                triggerTabList.forEach(function (triggerEl) {
                    var tabTrigger = new bootstrap.Tab(triggerEl);
                });
            }
        }

        function initializeEventListeners() {
            // JSON textarea change event
            $('#json_data').on('input', function() {
                const value = this.value.trim();
                if (value) {
                    validateAndParseJSON(value);
                } else {
                    clearJSONStatus();
                    disableConvertButton();
                }
            });

            // Export format change
            $('input[name="exportFormat"]').on('change', function() {
                updatePreviewFormat();
            });
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.add('drag-over');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.remove('drag-over');
        }

        function handleJSONDrop(e) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleJSONFileSelect({ target: { files: files } });
            }
        }

        function handleJSONFileSelect(e) {
            const files = e.target.files;
            if (files.length === 0) return;

            const file = files[0];
            if (validateJSONFile(file)) {
                currentFile = file;
                displayJSONFileInfo(file);
                readJSONFile(file);
            }
        }

        function validateJSONFile(file) {
            const validTypes = ['application/json', 'text/plain'];
            const validExtensions = ['.json', '.txt'];
            const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

            if (!validTypes.includes(file.type) && !validExtensions.includes(fileExtension)) {
                showError('Please select a valid JSON file (.json) or text file (.txt).');
                return false;
            }

            if (file.size > 10 * 1024 * 1024) { // 10MB limit
                showError('File size must be less than 10MB.');
                return false;
            }

            return true;
        }

        function displayJSONFileInfo(file) {
            document.getElementById('json-file-name').textContent = file.name;
            document.getElementById('json-file-size').textContent = formatFileSize(file.size);
            document.getElementById('json-file-info').style.display = 'block';
            document.getElementById('json-upload-zone').style.display = 'none';
        }

        function readJSONFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                document.getElementById('json_data').value = content;
                validateAndParseJSON(content);
            };
            reader.onerror = function() {
                showError('Error reading the file.');
            };
            reader.readAsText(file);
        }

        function clearJSONFile() {
            currentFile = null;
            document.getElementById('json_file_upload').value = '';
            document.getElementById('json-file-info').style.display = 'none';
            document.getElementById('json-upload-zone').style.display = 'block';
            document.getElementById('json_data').value = '';
            clearJSONStatus();
            disableConvertButton();
            hidePreview();
        }

        function validateAndParseJSON(jsonString) {
            try {
                const parsed = JSON.parse(jsonString);
                jsonData = parsed;
                showJSONStatus('valid', 'Valid JSON detected');
                enableConvertButton();
                return true;
            } catch (error) {
                jsonData = null;
                showJSONStatus('invalid', 'Invalid JSON: ' + error.message);
                disableConvertButton();
                return false;
            }
        }

        function showJSONStatus(type, message) {
            const statusElement = document.getElementById('json-status');
            statusElement.className = `json-status ${type}`;
            statusElement.innerHTML = `
                <i class="fas ${type === 'valid' ? 'fa-check-circle' : 'fa-exclamation-triangle'} me-2"></i>
                ${message}
            `;
            statusElement.style.display = 'block';
        }

        function clearJSONStatus() {
            const statusElement = document.getElementById('json-status');
            statusElement.style.display = 'none';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatInputJSON() {
            const textarea = document.getElementById('json_data');
            const value = textarea.value.trim();

            if (!value) {
                showError('No JSON data to format.');
                return;
            }

            try {
                const parsed = JSON.parse(value);
                const formatted = JSON.stringify(parsed, null, 2);
                textarea.value = formatted;
                showSuccess('JSON formatted successfully!');
                validateAndParseJSON(formatted);
            } catch (error) {
                showError('Cannot format invalid JSON.');
            }
        }

        function validateJSON() {
            const value = document.getElementById('json_data').value.trim();

            if (!value) {
                showError('No JSON data to validate.');
                return;
            }

            if (validateAndParseJSON(value)) {
                showSuccess('JSON is valid!');
            }
        }

        function clearJSON() {
            document.getElementById('json_data').value = '';
            clearJSONStatus();
            disableConvertButton();
            hidePreview();
            jsonData = null;
        }

        function enableConvertButton() {
            document.getElementById('convert-btn').disabled = false;
        }

        function disableConvertButton() {
            document.getElementById('convert-btn').disabled = true;
        }

        function showProgress() {
            document.getElementById('progress-container').style.display = 'block';
            updateProgress(0, 'Initializing...');
        }

        function hideProgress() {
            document.getElementById('progress-container').style.display = 'none';
        }

        function updateProgress(percent, text) {
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');

            progressBar.style.width = percent + '%';
            progressBar.setAttribute('aria-valuenow', percent);
            progressText.textContent = text;
        }

        function disableConvertButtonWithLoading() {
            const btn = document.getElementById('convert-btn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Converting...';
        }

        function enableConvertButtonNormal() {
            const btn = document.getElementById('convert-btn');
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-file-export me-2"></i>Convert to Excel';
        }

        function exportData() {
            if (!jsonData) {
                showError('No valid JSON data to convert.');
                return;
            }

            if (isProcessing) return;

            isProcessing = true;
            disableConvertButtonWithLoading();
            showProgress();

            // Simulate progress for better UX
            updateProgress(20, 'Preparing data...');

            setTimeout(() => {
                try {
                    updateProgress(40, 'Processing JSON...');

                    // Prepare data for Excel export
                    let dataToExport = prepareDataForExport(jsonData);

                    updateProgress(60, 'Creating spreadsheet...');

                    // Get selected format
                    const format = document.querySelector('input[name="exportFormat"]:checked').value;

                    updateProgress(80, 'Generating file...');

                    // Create and download file
                    createAndDownloadFile(dataToExport, format);

                    updateProgress(100, 'Complete!');

                    // Show preview
                    showPreview(dataToExport, format);

                    setTimeout(() => {
                        hideProgress();
                        enableConvertButtonNormal();
                        isProcessing = false;
                        showSuccess('Excel file generated successfully!');
                    }, 500);

                } catch (error) {
                    console.error('Export error:', error);
                    showError('Error converting JSON to Excel: ' + error.message);
                    hideProgress();
                    enableConvertButtonNormal();
                    isProcessing = false;
                }
            }, 500);
        }

        function prepareDataForExport(data) {
            // Handle different JSON structures
            if (Array.isArray(data)) {
                return data;
            } else if (typeof data === 'object' && data !== null) {
                // If it's an object, try to find arrays within it
                const arrays = [];
                for (const key in data) {
                    if (Array.isArray(data[key])) {
                        arrays.push({ sheetName: key, data: data[key] });
                    }
                }

                if (arrays.length > 0) {
                    return arrays;
                } else {
                    // Convert single object to array
                    return [data];
                }
            } else {
                throw new Error('JSON data must be an array or object');
            }
        }

        function createAndDownloadFile(data, format) {
            const wb = XLSX.utils.book_new();

            if (Array.isArray(data) && data.length > 0 && data[0].sheetName) {
                // Multiple sheets
                data.forEach(sheet => {
                    const ws = XLSX.utils.json_to_sheet(sheet.data);
                    XLSX.utils.book_append_sheet(wb, ws, sheet.sheetName);
                });
            } else {
                // Single sheet
                const ws = XLSX.utils.json_to_sheet(data);
                XLSX.utils.book_append_sheet(wb, ws, "Data");
            }

            const filename = generateFilename(format);

            if (format === 'csv') {
                // For CSV, only export the first sheet
                const firstSheet = wb.Sheets[wb.SheetNames[0]];
                const csv = XLSX.utils.sheet_to_csv(firstSheet);
                downloadCSV(csv, filename);
            } else {
                XLSX.writeFile(wb, filename);
            }

            previewData = { workbook: wb, format: format, filename: filename };
        }

        function generateFilename(format) {
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const baseName = currentFile ?
                currentFile.name.replace(/\.[^/.]+$/, '') :
                'converted-json';
            return `${baseName}-${timestamp}.${format}`;
        }

        function downloadCSV(csvContent, filename) {
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function showPreview(data, format) {
            const previewSection = document.getElementById('preview-section');
            const previewTable = document.getElementById('preview-table');
            const thead = previewTable.querySelector('thead');
            const tbody = previewTable.querySelector('tbody');

            // Clear existing content
            thead.innerHTML = '';
            tbody.innerHTML = '';

            // Get data for preview (first sheet if multiple)
            let previewDataArray = Array.isArray(data) && data.length > 0 && data[0].sheetName ?
                data[0].data : data;

            if (!Array.isArray(previewDataArray)) {
                previewDataArray = [previewDataArray];
            }

            if (previewDataArray.length === 0) {
                showError('No data to preview.');
                return;
            }

            // Get column headers
            const headers = Object.keys(previewDataArray[0]);

            // Create header row
            const headerRow = document.createElement('tr');
            headers.forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);

            // Create data rows (limit to first 10 for preview)
            const previewLimit = Math.min(10, previewDataArray.length);
            for (let i = 0; i < previewLimit; i++) {
                const row = document.createElement('tr');
                headers.forEach(header => {
                    const td = document.createElement('td');
                    const value = previewDataArray[i][header];
                    td.textContent = value !== null && value !== undefined ? value : '';
                    row.appendChild(td);
                });
                tbody.appendChild(row);
            }

            // Update statistics
            document.getElementById('preview-record-count').textContent = previewDataArray.length;
            document.getElementById('preview-column-count').textContent = headers.length;
            document.getElementById('preview-format').textContent = format.toUpperCase();
            document.getElementById('preview-timestamp').textContent = new Date().toLocaleString();

            // Show preview section
            previewSection.style.display = 'block';

            // Scroll to preview
            previewSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        function hidePreview() {
            document.getElementById('preview-section').style.display = 'none';
            previewData = null;
        }

        function downloadExcel() {
            if (!previewData) {
                showError('No file to download.');
                return;
            }

            if (previewData.format === 'csv') {
                const firstSheet = previewData.workbook.Sheets[previewData.workbook.SheetNames[0]];
                const csv = XLSX.utils.sheet_to_csv(firstSheet);
                downloadCSV(csv, previewData.filename);
            } else {
                XLSX.writeFile(previewData.workbook, previewData.filename);
            }

            showSuccess('File downloaded successfully!');
        }

        function previewMore() {
            if (!jsonData) {
                showError('No data to preview.');
                return;
            }

            // Show modal or expand preview (implement based on your needs)
            showSuccess('Full preview feature coming soon!');
        }

        function updatePreviewFormat() {
            const format = document.querySelector('input[name="exportFormat"]:checked').value;
            const formatDisplay = document.getElementById('preview-format');
            if (formatDisplay) {
                formatDisplay.textContent = format.toUpperCase();
            }
        }

        function showError(message) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: message,
                    confirmButtonColor: '#ef4444'
                });
            } else {
                alert('Error: ' + message);
            }
        }

        function showSuccess(message) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: message,
                    timer: 2000,
                    showConfirmButton: false,
                    confirmButtonColor: '#10b981'
                });
            } else {
                alert(message);
            }
        }
    </script>
@endsection
@section('css')
    <style>
        /* Custom styles for JSON to Excel converter */
        .text-gradient {
            background: linear-gradient(135deg, var(--info-color) 0%, var(--success-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: var(--font-weight-bold);
        }

        .bg-gradient-info {
            background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%) !important;
        }

        /* JSON Input Styles */
        .json-input-container {
            position: relative;
        }

        .json-textarea {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
            font-size: 0.875rem !important;
            line-height: 1.6 !important;
            resize: vertical;
            min-height: 300px;
            background: var(--bg-primary) !important;
            border: 2px solid var(--border-color) !important;
            border-radius: var(--radius-md) !important;
            transition: all var(--transition-fast) !important;
        }

        .json-textarea:focus {
            border-color: var(--info-color) !important;
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1) !important;
        }

        .json-input-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 0.5rem;
            opacity: 0;
            transition: opacity var(--transition-fast);
        }

        .json-input-container:hover .json-input-actions {
            opacity: 1;
        }

        .json-status {
            margin-top: var(--spacing-md);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            font-weight: var(--font-weight-medium);
            display: none;
        }

        .json-status.valid {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            color: #065f46;
            border-left: 4px solid var(--success-color);
        }

        .json-status.invalid {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            color: #991b1b;
            border-left: 4px solid var(--danger-color);
        }

        /* Dark mode JSON status */
        [data-theme="dark"] .json-status.valid {
            background: linear-gradient(135deg, #064e3b 0%, #065f46 100%);
            color: #a7f3d0;
        }

        [data-theme="dark"] .json-status.invalid {
            background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
            color: #fca5a5;
        }

        /* Upload Zone Styles (reused from Excel converter) */
        .upload-zone {
            border: 3px dashed var(--border-color);
            border-radius: var(--radius-xl);
            padding: 3rem 2rem;
            text-align: center;
            transition: all var(--transition-normal);
            cursor: pointer;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
            position: relative;
            overflow: hidden;
        }

        .upload-zone:hover {
            border-color: var(--info-color);
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, var(--bg-tertiary) 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .upload-zone.drag-over {
            border-color: var(--success-color);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, var(--bg-tertiary) 100%);
            transform: scale(1.02);
        }

        .upload-zone-content h4 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            font-weight: var(--font-weight-semibold);
        }

        .upload-zone-content p {
            color: var(--text-muted);
            margin-bottom: var(--spacing-lg);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--info-color);
            margin-bottom: var(--spacing-lg);
            transition: all var(--transition-normal);
        }

        .upload-zone:hover .upload-icon {
            transform: scale(1.1);
            color: #0891b2;
        }

        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .supported-formats {
            margin-top: var(--spacing-lg);
        }

        .supported-formats .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }

        /* File Info Styles */
        .file-info {
            background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-primary) 100%);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-top: var(--spacing-lg);
            animation: slideInRight var(--transition-normal) ease-out;
        }

        .file-info h5 {
            color: var(--text-primary);
            font-weight: var(--font-weight-semibold);
        }

        /* Progress Container */
        .progress-container {
            margin-top: var(--spacing-lg);
            animation: fadeInUp var(--transition-normal) ease-out;
        }

        .progress {
            height: 0.75rem;
            border-radius: var(--radius-md);
            background-color: var(--gray-200);
            overflow: hidden;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            background: linear-gradient(135deg, var(--info-color) 0%, var(--success-color) 100%);
            border-radius: var(--radius-md);
            transition: width var(--transition-slow) ease-in-out;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 35%, rgba(255,255,255,0.2) 35%, rgba(255,255,255,0.2) 65%, transparent 65%);
            background-size: 20px 20px;
            animation: progress-stripes 1s linear infinite;
        }

        @keyframes progress-stripes {
            0% { background-position: 0 0; }
            100% { background-position: 20px 0; }
        }

        /* Conversion Options */
        .conversion-options {
            margin-bottom: var(--spacing-md);
        }

        .conversion-options .form-check {
            margin-bottom: 0;
        }

        .conversion-options .form-check-label {
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: color var(--transition-fast);
        }

        .conversion-options .form-check-input:checked + .form-check-label {
            color: var(--info-color);
        }

        /* Enhanced Tabs */
        .nav-tabs {
            border-bottom: 2px solid var(--border-color);
            margin-bottom: var(--spacing-lg);
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: var(--radius-md) var(--radius-md) 0 0;
            padding: var(--spacing-md) var(--spacing-lg);
            font-weight: var(--font-weight-medium);
            color: var(--text-muted);
            transition: all var(--transition-fast);
            margin-right: var(--spacing-sm);
            background: transparent;
        }

        .nav-tabs .nav-link:hover {
            background-color: var(--bg-tertiary);
            color: var(--text-secondary);
            border-color: transparent;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%);
            color: #ffffff;
            border-color: var(--info-color);
            border-bottom: 2px solid var(--info-color);
        }

        /* Preview Table Styles */
        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
            border-radius: var(--radius-md);
        }

        .table {
            margin-bottom: 0;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
            border-bottom: 2px solid var(--border-color);
            font-weight: var(--font-weight-semibold);
            color: var(--text-secondary);
            padding: var(--spacing-md);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr {
            transition: all var(--transition-fast);
        }

        .table tbody tr:hover {
            background-color: var(--bg-tertiary);
            transform: scale(1.01);
        }

        .table tbody td {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
            color: var(--text-secondary);
            font-size: 0.875rem;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Preview Section Animation */
        #preview-section {
            animation: fadeInUp var(--transition-slow) ease-out;
        }

        /* Button Enhancements */
        .btn-group .btn {
            border-radius: var(--radius-md);
            margin-left: 0.25rem;
        }

        .btn-group .btn:first-child {
            margin-left: 0;
        }

        /* Responsive Improvements */
        @media (max-width: 768px) {
            .upload-zone {
                padding: 2rem 1rem;
            }

            .upload-icon {
                font-size: 2rem;
            }

            .json-textarea {
                min-height: 200px;
            }

            .json-input-actions {
                position: static;
                opacity: 1;
                margin-top: var(--spacing-md);
                justify-content: center;
            }

            .btn-group {
                flex-direction: column;
                width: 100%;
            }

            .btn-group .btn {
                margin: 0.25rem 0 0 0;
                width: 100%;
            }

            .conversion-options .d-flex {
                flex-direction: column;
                gap: 0.5rem;
            }

            .table-responsive {
                max-height: 300px;
            }

            .table tbody td {
                max-width: 150px;
            }
        }

        /* Dark Mode Specific Styles */
        [data-theme="dark"] .upload-zone {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .upload-zone:hover {
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, var(--bg-tertiary) 100%);
        }

        [data-theme="dark"] .file-info {
            background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-primary) 100%);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .progress {
            background-color: var(--bg-tertiary);
        }

        [data-theme="dark"] .nav-tabs {
            border-bottom-color: var(--border-color);
        }

        [data-theme="dark"] .nav-tabs .nav-link:hover {
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
        }

        [data-theme="dark"] .table thead th {
            background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
            border-bottom-color: var(--border-color);
            color: var(--text-secondary);
        }

        [data-theme="dark"] .table tbody tr:hover {
            background-color: var(--bg-tertiary);
        }

        [data-theme="dark"] .table tbody td {
            border-bottom-color: var(--border-color);
            color: var(--text-secondary);
        }
    </style>
@endsection
