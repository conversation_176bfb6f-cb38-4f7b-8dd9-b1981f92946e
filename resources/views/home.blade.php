@extends('layout/base')

@section('body')
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <div class="hero-content animate-fade-in-up">
                        <h1 class="hero-title text-gradient mb-3">
                            <i class="fas fa-tools mr-3"></i>
                            Tool Manager Dashboard
                        </h1>
                        <p class="hero-subtitle mb-4">
                            Your comprehensive suite of productivity tools and utilities.
                            Streamline your workflow with our powerful collection of management and conversion tools.
                        </p>
                        <div class="hero-stats">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <div class="stat-number">5</div>
                                        <div class="stat-label">Active Tools</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <div class="stat-number">2</div>
                                        <div class="stat-label">Categories</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <div class="stat-number">∞</div>
                                        <div class="stat-label">Possibilities</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tools Section -->
    <div class="container">
        <!-- Project Management Tools -->
        <div class="tools-section animate-fade-in-up">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-project-diagram text-primary mr-3"></i>
                    Project Management Tools
                </h2>
                <p class="section-description">
                    Essential tools for project planning, tracking, and communication
                </p>
            </div>

            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="tool-card" data-tool="qr-generator">
                        <div class="tool-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <div class="tool-content">
                            <h3 class="tool-title">QR Generator</h3>
                            <p class="tool-description">
                                Create custom QR codes for URLs, text, and contact information.
                                Perfect for sharing links and digital content.
                            </p>
                            <div class="tool-features">
                                <span class="feature-tag">
                                    <i class="fas fa-link"></i> URL Encoding
                                </span>
                                <span class="feature-tag">
                                    <i class="fas fa-download"></i> Download
                                </span>
                            </div>
                        </div>
                        <div class="tool-footer">
                            <a href="{{ route('qr_generator') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-arrow-right mr-2"></i>
                                Launch Tool
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="tool-card" data-tool="em-asia-report">
                        <div class="tool-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="tool-content">
                            <h3 class="tool-title">EM Asia Report Generator</h3>
                            <p class="tool-description">
                                Generate comprehensive reports for emerging markets in Asia.
                                Analyze data and create professional documentation.
                            </p>
                            <div class="tool-features">
                                <span class="feature-tag">
                                    <i class="fas fa-chart-bar"></i> Analytics
                                </span>
                                <span class="feature-tag">
                                    <i class="fas fa-file-pdf"></i> PDF Export
                                </span>
                            </div>
                        </div>
                        <div class="tool-footer">
                            <a href="{{ route('em_asia.report') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-arrow-right mr-2"></i>
                                Launch Tool
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="tool-card" data-tool="link-generator">
                        <div class="tool-icon">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                        <div class="tool-content">
                            <h3 class="tool-title">Link Generator</h3>
                            <p class="tool-description">
                                Vistream Autologin link generator for secure, one-click access.
                                Simplifies user authentication and session management.
                            </p>
                            <div class="tool-features">
                                <span class="feature-tag">
                                    <i class="fas fa-eye"></i> Tracking
                                </span>
                                <span class="feature-tag">
                                    <i class="fas fa-edit"></i> Custom URLs
                                </span>
                            </div>
                        </div>
                        <div class="tool-footer">
                            <a href="{{ route('link.generator') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-arrow-right mr-2"></i>
                                Launch Tool
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Software Tools -->
        <div class="tools-section animate-fade-in-up">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-code text-info mr-3"></i>
                    Software Tools
                </h2>
                <p class="section-description">
                    Data conversion and processing utilities for developers and analysts
                </p>
            </div>

            <div class="row">
                <div class="col-lg-6 col-md-6 mb-4">
                    <div class="tool-card" data-tool="excel-to-json">
                        <div class="tool-icon">
                            <i class="fas fa-file-excel"></i>
                        </div>
                        <div class="tool-content">
                            <h3 class="tool-title">Excel to JSON</h3>
                            <p class="tool-description">
                                Convert Excel spreadsheets to JSON format quickly and efficiently.
                                Perfect for data migration and API integration.
                            </p>
                            <div class="tool-features">
                                <span class="feature-tag">
                                    <i class="fas fa-upload"></i> Batch Upload
                                </span>
                                <span class="feature-tag">
                                    <i class="fas fa-code"></i> JSON Output
                                </span>
                            </div>
                        </div>
                        <div class="tool-footer">
                            <a href="{{ route('excel_to_json') }}" class="btn btn-info btn-block">
                                <i class="fas fa-arrow-right mr-2"></i>
                                Launch Tool
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 col-md-6 mb-4">
                    <div class="tool-card" data-tool="json-to-excel">
                        <div class="tool-icon">
                            <i class="fas fa-file-code"></i>
                        </div>
                        <div class="tool-content">
                            <h3 class="tool-title">JSON to Excel</h3>
                            <p class="tool-description">
                                Transform JSON data into Excel spreadsheets with proper formatting.
                                Ideal for data analysis and reporting.
                            </p>
                            <div class="tool-features">
                                <span class="feature-tag">
                                    <i class="fas fa-table"></i> Structured Data
                                </span>
                                <span class="feature-tag">
                                    <i class="fas fa-download"></i> Excel Export
                                </span>
                            </div>
                        </div>
                        <div class="tool-footer">
                            <a href="{{ route('json_to_excel') }}" class="btn btn-info btn-block">
                                <i class="fas fa-arrow-right mr-2"></i>
                                Launch Tool
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('css')
    <style>
        /* ===== HERO SECTION ===== */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-light) 0%, var(--bg-primary) 100%);
            padding: 4rem 0 3rem;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(79,70,229,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            line-height: 1.6;
            max-width: 600px;
            margin: 0 auto;
        }

        .hero-stats {
            margin-top: 2rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-top: 0.5rem;
        }

        /* ===== TOOLS SECTION ===== */
        .tools-section {
            margin-bottom: 4rem;
        }

        .section-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 2.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .section-description {
            font-size: 1.125rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* ===== TOOL CARDS ===== */
        .tool-card {
            background: var(--bg-primary);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: 2rem;
            height: 100%;
            display: flex;
            flex-direction: column;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .tool-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.05), transparent);
            transition: left var(--transition-slow);
        }

        .tool-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-color);
        }

        .tool-card:hover::before {
            left: 100%;
        }

        .tool-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            transition: all var(--transition-normal);
        }

        .tool-icon i {
            font-size: 2rem;
            color: white;
        }

        .tool-card:hover .tool-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .tool-content {
            flex: 1;
            text-align: center;
        }

        .tool-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .tool-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .tool-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .feature-tag {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .tool-footer {
            margin-top: auto;
        }

        /* ===== QUICK ACTIONS ===== */
        .quick-actions-section {
            margin-bottom: 3rem;
        }

        .quick-action-btn {
            height: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            transition: all var(--transition-normal);
            border-width: 2px;
        }

        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }

        .quick-action-btn i {
            font-size: 1.5rem;
        }

        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .section-title {
                font-size: 1.75rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .tool-card {
                padding: 1.5rem;
            }

            .tool-icon {
                width: 60px;
                height: 60px;
            }

            .tool-icon i {
                font-size: 1.5rem;
            }

            .quick-action-btn {
                height: 80px;
            }
        }

        /* ===== DARK MODE ENHANCEMENTS ===== */
        [data-theme="dark"] .hero-section {
            background: linear-gradient(135deg, var(--primary-light) 0%, var(--bg-secondary) 100%);
        }

        [data-theme="dark"] .feature-tag {
            background: var(--bg-secondary);
            color: var(--text-muted);
        }

        [data-theme="dark"] .tool-card {
            background: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .tool-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* ===== ANIMATIONS ===== */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .tool-card:nth-child(odd) {
            animation: float 6s ease-in-out infinite;
        }

        .tool-card:nth-child(even) {
            animation: float 6s ease-in-out infinite reverse;
        }

        /* Pause animations on hover */
        .tool-card:hover {
            animation-play-state: paused;
        }
    </style>
@endsection

@section('js')
    <script>
        // ===== INTERACTIVE FEATURES =====
        document.addEventListener('DOMContentLoaded', function() {
            initializeToolCards();
            initializeQuickActions();
            initializeAnimations();
            initializeTooltips();
        });

        function initializeToolCards() {
            const toolCards = document.querySelectorAll('.tool-card');

            toolCards.forEach(card => {
                // Add click handler for entire card
                card.addEventListener('click', function(e) {
                    if (!e.target.closest('.btn')) {
                        const link = card.querySelector('.btn');
                        if (link) {
                            window.location.href = link.href;
                        }
                    }
                });

                // Add keyboard navigation
                card.setAttribute('tabindex', '0');
                card.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        const link = card.querySelector('.btn');
                        if (link) {
                            link.click();
                        }
                    }
                });

                // Add hover effects
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                });
            });
        }

        function initializeQuickActions() {
            // Recent Files functionality
            window.showRecentFiles = function() {
                Swal.fire({
                    title: '<i class="fas fa-history text-primary"></i> Recent Files',
                    html: `
                        <div class="text-left">
                            <div class="list-group">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-file-excel text-success mr-2"></i>
                                        <strong>data_export.xlsx</strong>
                                        <br><small class="text-muted">Converted 2 hours ago</small>
                                    </div>
                                    <span class="badge badge-success">Excel</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-qrcode text-primary mr-2"></i>
                                        <strong>website_qr.png</strong>
                                        <br><small class="text-muted">Generated yesterday</small>
                                    </div>
                                    <span class="badge badge-primary">QR Code</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-chart-line text-info mr-2"></i>
                                        <strong>asia_report_q3.pdf</strong>
                                        <br><small class="text-muted">Generated last week</small>
                                    </div>
                                    <span class="badge badge-info">Report</span>
                                </div>
                            </div>
                        </div>
                    `,
                    showCloseButton: true,
                    showConfirmButton: false,
                    width: '600px'
                });
            };

            // Help & Documentation
            window.showHelp = function() {
                Swal.fire({
                    title: '<i class="fas fa-question-circle text-success"></i> Help & Documentation',
                    html: `
                        <div class="text-left">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5><i class="fas fa-book text-primary mr-2"></i>Quick Start</h5>
                                    <ul class="list-unstyled">
                                        <li><a href="#" class="text-decoration-none"><i class="fas fa-play-circle mr-1"></i> Getting Started</a></li>
                                        <li><a href="#" class="text-decoration-none"><i class="fas fa-video mr-1"></i> Video Tutorials</a></li>
                                        <li><a href="#" class="text-decoration-none"><i class="fas fa-file-alt mr-1"></i> User Guide</a></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h5><i class="fas fa-tools text-warning mr-2"></i>Tool Guides</h5>
                                    <ul class="list-unstyled">
                                        <li><a href="#" class="text-decoration-none"><i class="fas fa-qrcode mr-1"></i> QR Generator Guide</a></li>
                                        <li><a href="#" class="text-decoration-none"><i class="fas fa-file-excel mr-1"></i> Excel Conversion</a></li>
                                        <li><a href="#" class="text-decoration-none"><i class="fas fa-chart-line mr-1"></i> Report Generation</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `,
                    showCloseButton: true,
                    showConfirmButton: false,
                    width: '700px'
                });
            };

            // Settings
            window.showSettings = function() {
                Swal.fire({
                    title: '<i class="fas fa-cog text-info"></i> Settings',
                    html: `
                        <div class="text-left">
                            <div class="form-group">
                                <label><i class="fas fa-palette mr-2"></i>Theme Preference</label>
                                <div class="btn-group btn-group-toggle d-flex" data-toggle="buttons">
                                    <label class="btn btn-outline-primary flex-fill">
                                        <input type="radio" name="theme" value="light"> Light
                                    </label>
                                    <label class="btn btn-outline-primary flex-fill">
                                        <input type="radio" name="theme" value="dark"> Dark
                                    </label>
                                    <label class="btn btn-outline-primary flex-fill">
                                        <input type="radio" name="theme" value="auto"> Auto
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-bell mr-2"></i>Notifications</label>
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="notifications" checked>
                                    <label class="custom-control-label" for="notifications">Enable notifications</label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-download mr-2"></i>Auto-download</label>
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="autodownload">
                                    <label class="custom-control-label" for="autodownload">Auto-download generated files</label>
                                </div>
                            </div>
                        </div>
                    `,
                    showCancelButton: true,
                    confirmButtonText: '<i class="fas fa-save mr-1"></i> Save Settings',
                    cancelButtonText: 'Cancel',
                    width: '500px',
                    preConfirm: () => {
                        // Handle theme change
                        const selectedTheme = document.querySelector('input[name="theme"]:checked')?.value;
                        if (selectedTheme && window.ThemeUtils) {
                            if (selectedTheme === 'auto') {
                                window.ThemeUtils.resetToSystem();
                            } else {
                                window.ThemeUtils.setTheme(selectedTheme);
                            }
                        }

                        Swal.fire({
                            icon: 'success',
                            title: 'Settings Saved!',
                            text: 'Your preferences have been updated.',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    }
                });

                // Set current theme selection
                setTimeout(() => {
                    const currentTheme = window.ThemeUtils?.getCurrentTheme() || 'light';
                    const themeRadio = document.querySelector(`input[name="theme"][value="${currentTheme}"]`);
                    if (themeRadio) {
                        themeRadio.checked = true;
                        themeRadio.closest('label').classList.add('active');
                    }
                }, 100);
            };

            // Feedback
            window.showFeedback = function() {
                Swal.fire({
                    title: '<i class="fas fa-comment text-warning"></i> Send Feedback',
                    html: `
                        <div class="text-left">
                            <div class="form-group">
                                <label for="feedbackType">Feedback Type</label>
                                <select class="form-control" id="feedbackType">
                                    <option value="suggestion">Suggestion</option>
                                    <option value="bug">Bug Report</option>
                                    <option value="feature">Feature Request</option>
                                    <option value="general">General Feedback</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="feedbackMessage">Your Message</label>
                                <textarea class="form-control" id="feedbackMessage" rows="4"
                                    placeholder="Tell us what you think or report any issues..."></textarea>
                            </div>
                            <div class="form-group">
                                <label for="feedbackEmail">Email (optional)</label>
                                <input type="email" class="form-control" id="feedbackEmail"
                                    placeholder="<EMAIL>">
                            </div>
                        </div>
                    `,
                    showCancelButton: true,
                    confirmButtonText: '<i class="fas fa-paper-plane mr-1"></i> Send Feedback',
                    cancelButtonText: 'Cancel',
                    width: '600px',
                    preConfirm: () => {
                        const type = document.getElementById('feedbackType').value;
                        const message = document.getElementById('feedbackMessage').value;
                        const email = document.getElementById('feedbackEmail').value;

                        if (!message.trim()) {
                            Swal.showValidationMessage('Please enter your feedback message');
                            return false;
                        }

                        // Here you would typically send the feedback to your backend
                        console.log('Feedback:', { type, message, email });

                        return { type, message, email };
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Thank You!',
                            text: 'Your feedback has been sent successfully.',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    }
                });
            };
        }

        function initializeAnimations() {
            // Intersection Observer for scroll animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all animated elements
            document.querySelectorAll('.animate-fade-in-up').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        }

        function initializeTooltips() {
            // Initialize Bootstrap tooltips if available
            if (typeof $!== 'undefined' && $.fn.tooltip) {
                $('[data-toggle="tooltip"]').tooltip();
            }
        }

        // Add some interactive stats counter animation
        function animateStats() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = stat.textContent;
                if (!isNaN(finalValue)) {
                    let currentValue = 0;
                    const increment = finalValue / 50;
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            stat.textContent = finalValue;
                            clearInterval(timer);
                        } else {
                            stat.textContent = Math.floor(currentValue);
                        }
                    }, 30);
                }
            });
        }

        // Run stats animation when page loads
        setTimeout(animateStats, 500);
    </script>
@endsection
