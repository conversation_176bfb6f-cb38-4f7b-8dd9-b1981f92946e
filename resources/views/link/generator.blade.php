@extends('layout/base', ['title' => 'Link Generator'])

@section('body')
    <div class="container pb-5">
        <!-- Enhanced Header Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="text-center animate-fade-in-up">
                    <h1 class="display-4 text-gradient mb-3">
                        <i class="fas fa-link mr-3"></i>Smart Link Generator
                    </h1>
                    <p class="lead text-muted mb-4">Create powerful, trackable links with advanced features and analytics</p>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center bg-transparent">
                            <li class="breadcrumb-item">
                                <a href="{{ route('home') }}" class="text-decoration-none">
                                    <i class="fas fa-home mr-1"></i>Dashboard
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">Link Generator</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Statistics Dashboard -->
        <div class="row mb-5">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card bg-gradient-primary text-white shadow-modern animate-slide-in-right" style="animation-delay: 0.1s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">Total Links</h5>
                                <h2 class="mb-0" id="total-links">0</h2>
                            </div>
                            <div class="ml-3">
                                <i class="fas fa-link fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card bg-gradient-success text-white shadow-modern animate-slide-in-right" style="animation-delay: 0.2s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">Active Links</h5>
                                <h2 class="mb-0" id="active-links">0</h2>
                            </div>
                            <div class="ml-3">
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card bg-gradient-info text-white shadow-modern animate-slide-in-right" style="animation-delay: 0.3s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">Total Clicks</h5>
                                <h2 class="mb-0" id="total-clicks">0</h2>
                            </div>
                            <div class="ml-3">
                                <i class="fas fa-mouse-pointer fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card bg-gradient-warning text-white shadow-modern animate-slide-in-right" style="animation-delay: 0.4s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">This Month</h5>
                                <h2 class="mb-0" id="month-clicks">0</h2>
                            </div>
                            <div class="ml-3">
                                <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <div class="col-lg-8">
                <!-- Link Generator Card -->
                <div class="card shadow-modern animate-fade-in-up" style="animation-delay: 0.5s">
                    <div class="card-header bg-gradient-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-magic mr-2"></i>Link Generator
                            </h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-light btn-sm" id="reset-all-forms">
                                    <i class="fas fa-undo mr-1"></i>Reset All
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-header p-0 pt-1 bg-white">
                        <ul class="nav nav-tabs nav-tabs-modern" id="link-generator-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="autologin-tab" data-toggle="pill"
                                   href="#autologin-link" role="tab" aria-controls="autologin-link" aria-selected="true">
                                    <i class="fas fa-sign-in-alt mr-2"></i>Autologin Link
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="shortener-tab" data-toggle="pill"
                                   href="#url-shortener" role="tab" aria-controls="url-shortener" aria-selected="false">
                                    <i class="fas fa-compress-alt mr-2"></i>URL Shortener
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="tracking-tab" data-toggle="pill"
                                   href="#tracking-link" role="tab" aria-controls="tracking-link" aria-selected="false">
                                    <i class="fas fa-chart-line mr-2"></i>Tracking Link
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="custom-tab" data-toggle="pill"
                                   href="#custom-redirect" role="tab" aria-controls="custom-redirect" aria-selected="false">
                                    <i class="fas fa-cogs mr-2"></i>Custom Redirect
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="link-generator-content">
                            <!-- Autologin Link Tab -->
                            <div class="tab-pane fade show active" id="autologin-link" role="tabpanel" aria-labelledby="autologin-tab">
                                <form id="autologin-link-generator" class="link-form">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="domain" class="form-label">
                                                    <i class="fas fa-globe mr-2"></i>Website Domain *
                                                </label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">https://</span>
                                                    </div>
                                                    <input type="text" name="domain" id="domain" placeholder="vistream.tv"
                                                           class="form-control form-control-modern" required>
                                                </div>
                                                <div class="invalid-feedback"></div>
                                                <small class="form-text text-muted">Enter domain without protocol</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="autologin-key" class="form-label">
                                                    <i class="fas fa-key mr-2"></i>Autologin Key *
                                                </label>
                                                <div class="input-group">
                                                    <input type="password" name="key" id="autologin-key"
                                                           placeholder="Enter your autologin key"
                                                           class="form-control form-control-modern" required>
                                                    <div class="input-group-append">
                                                        <button class="btn btn-outline-secondary" type="button" id="toggle-key-visibility">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="user-email" class="form-label">
                                                    <i class="fas fa-envelope mr-2"></i>Email Address *
                                                </label>
                                                <input type="email" name="email" id="user-email" placeholder="<EMAIL>"
                                                       class="form-control form-control-modern" required>
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="first-name" class="form-label">
                                                    <i class="fas fa-user mr-2"></i>First Name
                                                </label>
                                                <input type="text" name="first_name" id="first-name" placeholder="John"
                                                       class="form-control form-control-modern">
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="last-name" class="form-label">
                                                    <i class="fas fa-user mr-2"></i>Last Name
                                                </label>
                                                <input type="text" name="last_name" id="last-name" placeholder="Doe"
                                                       class="form-control form-control-modern">
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Advanced Options -->
                                    <div class="card bg-light mt-3">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <button class="btn btn-link p-0" type="button" data-toggle="collapse"
                                                        data-target="#autologin-advanced" aria-expanded="false">
                                                    <i class="fas fa-cog mr-2"></i>Advanced Options
                                                    <i class="fas fa-chevron-down ml-2"></i>
                                                </button>
                                            </h6>
                                        </div>
                                        <div class="collapse" id="autologin-advanced">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="redirect-url" class="form-label">Redirect After Login</label>
                                                            <input type="url" name="redirect_url" id="redirect-url"
                                                                   placeholder="https://example.com/dashboard"
                                                                   class="form-control form-control-modern">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="session-duration" class="form-label">Session Duration</label>
                                                            <select name="session_duration" id="session-duration"
                                                                    class="form-control form-control-modern">
                                                                <option value="">Default</option>
                                                                <option value="1h">1 Hour</option>
                                                                <option value="24h">24 Hours</option>
                                                                <option value="7d">7 Days</option>
                                                                <option value="30d">30 Days</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="custom-control custom-switch">
                                                            <input type="checkbox" class="custom-control-input" id="track-autologin">
                                                            <label class="custom-control-label" for="track-autologin">
                                                                Enable click tracking for this link
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group mt-4">
                                        <button type="submit" class="btn btn-primary btn-lg btn-generate">
                                            <i class="fas fa-magic mr-2"></i>Generate Autologin Link
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-lg ml-2" id="preview-autologin">
                                            <i class="fas fa-eye mr-2"></i>Preview
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- URL Shortener Tab -->
                            <div class="tab-pane fade" id="url-shortener" role="tabpanel" aria-labelledby="shortener-tab">
                                <form id="url-shortener-generator" class="link-form">
                                    <div class="form-group">
                                        <label for="long-url" class="form-label">
                                            <i class="fas fa-link mr-2"></i>Long URL *
                                        </label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">
                                                    <i class="fas fa-globe"></i>
                                                </span>
                                            </div>
                                            <input type="url" name="long_url" id="long-url"
                                                   placeholder="https://example.com/very/long/url/that/needs/shortening"
                                                   class="form-control form-control-modern" required>
                                        </div>
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="custom-alias" class="form-label">
                                                    <i class="fas fa-edit mr-2"></i>Custom Alias (Optional)
                                                </label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">short.ly/</span>
                                                    </div>
                                                    <input type="text" name="custom_alias" id="custom-alias"
                                                           placeholder="my-custom-link"
                                                           class="form-control form-control-modern">
                                                </div>
                                                <small class="form-text text-muted">Leave empty for auto-generated alias</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="expiration-date" class="form-label">
                                                    <i class="fas fa-calendar-times mr-2"></i>Expiration Date
                                                </label>
                                                <input type="datetime-local" name="expiration_date" id="expiration-date"
                                                       class="form-control form-control-modern">
                                                <small class="form-text text-muted">Leave empty for permanent link</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" class="btn btn-success btn-lg btn-generate">
                                            <i class="fas fa-compress-alt mr-2"></i>Shorten URL
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Live Preview & Results Panel -->
            <div class="col-lg-4">
                <div class="row">
                    <!-- Live Preview Card -->
                    <div class="col-12 mb-4">
                        <div class="card shadow-modern animate-slide-in-right" style="animation-delay: 0.6s">
                            <div class="card-header bg-gradient-info text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-eye mr-2"></i>Live Preview
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="link-preview-container">
                                    <div class="link-placeholder text-center">
                                        <i class="fas fa-link fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Generated link will appear here</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions Card -->
                    <div class="col-12 mb-4">
                        <div class="card shadow-modern animate-slide-in-right" style="animation-delay: 0.7s">
                            <div class="card-header bg-gradient-warning text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-bolt mr-2"></i>Quick Actions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary btn-sm" id="copy-link" disabled>
                                        <i class="fas fa-copy mr-2"></i>Copy Link
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" id="test-link" disabled>
                                        <i class="fas fa-external-link-alt mr-2"></i>Test Link
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" id="qr-code" disabled>
                                        <i class="fas fa-qrcode mr-2"></i>Generate QR
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" id="share-link" disabled>
                                        <i class="fas fa-share-alt mr-2"></i>Share
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Links Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card shadow-modern animate-fade-in-up" style="animation-delay: 0.8s">
                    <div class="card-header bg-gradient-secondary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-history mr-2"></i>Recent Links
                            </h5>
                            <button class="btn btn-light btn-sm" id="clear-history">
                                <i class="fas fa-trash mr-1"></i>Clear History
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="recent-links-container">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-history fa-2x mb-3"></i>
                                <p>No recent links. Generate your first link to see it here!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        // Enhanced Link Generator with modern features
        class SmartLinkGenerator {
            constructor() {
                this.recentLinks = JSON.parse(localStorage.getItem('recentLinks') || '[]');
                this.currentLink = null;
                this.validation = {
                    domainPattern: /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/,
                    emailPattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    urlPattern: /^https?:\/\/.+/
                };
                this.init();
            }

            init() {
                this.initEventListeners();
                this.initFormValidation();
                this.loadRecentLinks();
                this.updateStatistics();
                this.initLivePreview();
            }

            initEventListeners() {
                // Form submissions
                $('#autologin-link-generator').on('submit', (e) => {
                    e.preventDefault();
                    this.generateAutologinLink();
                });

                $('#url-shortener-generator').on('submit', (e) => {
                    e.preventDefault();
                    this.generateShortUrl();
                });

                // Key visibility toggle
                $('#toggle-key-visibility').on('click', () => this.toggleKeyVisibility());

                // Reset forms
                $('#reset-all-forms').on('click', () => this.resetAllForms());

                // Quick actions
                $('#copy-link').on('click', () => this.copyLink());
                $('#test-link').on('click', () => this.testLink());
                $('#qr-code').on('click', () => this.generateQRCode());
                $('#share-link').on('click', () => this.shareLink());

                // Preview buttons
                $('#preview-autologin').on('click', () => this.previewAutologinLink());

                // Clear history
                $('#clear-history').on('click', () => this.clearHistory());

                // Live preview updates
                $('.form-control-modern').on('input', () => {
                    clearTimeout(this.previewTimeout);
                    this.previewTimeout = setTimeout(() => this.updateLivePreview(), 500);
                });

                // Tab change events
                $('.nav-link').on('shown.bs.tab', () => {
                    this.updateLivePreview();
                    this.resetQuickActions();
                });
            }

            initFormValidation() {
                // Real-time validation for all forms
                $('.form-control-modern').on('blur', (e) => {
                    const field = $(e.target);
                    this.validateField(field);
                });

                // Domain validation
                $('#domain').on('input', (e) => {
                    const domain = e.target.value;
                    if (domain && !this.validation.domainPattern.test(domain)) {
                        $(e.target).addClass('is-invalid');
                        $(e.target).siblings('.invalid-feedback').text('Please enter a valid domain (e.g., example.com)');
                    } else if (domain) {
                        $(e.target).removeClass('is-invalid').addClass('is-valid');
                    }
                });

                // Email validation
                $('#user-email').on('input', (e) => {
                    const email = e.target.value;
                    if (email && !this.validation.emailPattern.test(email)) {
                        $(e.target).addClass('is-invalid');
                        $(e.target).siblings('.invalid-feedback').text('Please enter a valid email address');
                    } else if (email) {
                        $(e.target).removeClass('is-invalid').addClass('is-valid');
                    }
                });

                // URL validation
                $('#long-url').on('input', (e) => {
                    const url = e.target.value;
                    if (url && !this.validation.urlPattern.test(url)) {
                        $(e.target).addClass('is-invalid');
                        $(e.target).siblings('.invalid-feedback').text('Please enter a valid URL starting with http:// or https://');
                    } else if (url) {
                        $(e.target).removeClass('is-invalid').addClass('is-valid');
                    }
                });
            }

            validateField(field) {
                const value = field.val().trim();
                const type = field.attr('type');
                const required = field.prop('required');

                field.removeClass('is-valid is-invalid');
                field.siblings('.invalid-feedback').text('');

                if (required && !value) {
                    field.addClass('is-invalid');
                    field.siblings('.invalid-feedback').text('This field is required.');
                    return false;
                }

                if (value) {
                    if (type === 'email' && !this.validation.emailPattern.test(value)) {
                        field.addClass('is-invalid');
                        field.siblings('.invalid-feedback').text('Please enter a valid email address.');
                        return false;
                    }

                    if (type === 'url' && !this.validation.urlPattern.test(value)) {
                        field.addClass('is-invalid');
                        field.siblings('.invalid-feedback').text('Please enter a valid URL.');
                        return false;
                    }

                    field.addClass('is-valid');
                }

                return true;
            }

            generateAutologinLink() {
                if (!this.validateAutologinForm()) {
                    return;
                }

                const formData = this.getFormData('#autologin-link-generator');
                const url = this.buildAutologinUrl(formData);

                this.showGeneratedLink(url, 'autologin');
                this.saveToHistory(url, 'Autologin Link', formData.domain);
                this.showSuccess('Autologin link generated successfully!');
            }

            generateShortUrl() {
                if (!this.validateShortenerForm()) {
                    return;
                }

                const formData = this.getFormData('#url-shortener-generator');
                // For demo purposes, we'll create a mock short URL
                const shortUrl = this.buildShortUrl(formData);

                this.showGeneratedLink(shortUrl, 'shortener');
                this.saveToHistory(shortUrl, 'Short URL', formData.long_url);
                this.showSuccess('Short URL generated successfully!');
            }

            buildAutologinUrl(data) {
                let url = `https://${data.domain}/autologin?key=${encodeURIComponent(data.key)}&email=${encodeURIComponent(data.email)}`;

                if (data.first_name) {
                    url += `&first_name=${encodeURIComponent(data.first_name)}`;
                }

                if (data.last_name) {
                    url += `&last_name=${encodeURIComponent(data.last_name)}`;
                }

                if (data.redirect_url) {
                    url += `&redirect=${encodeURIComponent(data.redirect_url)}`;
                }

                if (data.session_duration) {
                    url += `&duration=${encodeURIComponent(data.session_duration)}`;
                }

                return url;
            }

            buildShortUrl(data) {
                // Mock short URL generation - in real implementation, this would call a backend service
                const alias = data.custom_alias || this.generateRandomAlias();
                return `https://short.ly/${alias}`;
            }

            generateRandomAlias() {
                const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                let result = '';
                for (let i = 0; i < 6; i++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return result;
            }

            validateAutologinForm() {
                const form = $('#autologin-link-generator');
                const requiredFields = form.find('[required]');
                let isValid = true;

                requiredFields.each((i, field) => {
                    if (!this.validateField($(field))) {
                        isValid = false;
                    }
                });

                return isValid;
            }

            validateShortenerForm() {
                const form = $('#url-shortener-generator');
                const requiredFields = form.find('[required]');
                let isValid = true;

                requiredFields.each((i, field) => {
                    if (!this.validateField($(field))) {
                        isValid = false;
                    }
                });

                return isValid;
            }

            getFormData(formSelector) {
                const formArray = $(formSelector).serializeArray();
                const formData = {};

                formArray.forEach(item => {
                    formData[item.name] = item.value;
                });

                return formData;
            }

            showGeneratedLink(url, type) {
                this.currentLink = { url, type };

                const previewHtml = `
                    <div class="generated-link-preview">
                        <div class="link-info mb-3">
                            <label class="form-label">Generated Link:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" value="${url}" readonly>
                                <div class="input-group-append">
                                    <button class="btn btn-outline-primary" onclick="linkGenerator.copyToClipboard('${url}')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="link-stats">
                            <small class="text-muted">
                                <i class="fas fa-info-circle mr-1"></i>
                                Link created on ${new Date().toLocaleString()}
                            </small>
                        </div>
                    </div>
                `;

                $('#link-preview-container').html(previewHtml);
                this.enableQuickActions();
            }

            enableQuickActions() {
                $('#copy-link, #test-link, #qr-code, #share-link').prop('disabled', false);
            }

            resetQuickActions() {
                $('#copy-link, #test-link, #qr-code, #share-link').prop('disabled', true);
                this.currentLink = null;
            }

            copyLink() {
                if (!this.currentLink) return;
                this.copyToClipboard(this.currentLink.url);
            }

            copyToClipboard(text) {
                navigator.clipboard.writeText(text).then(() => {
                    this.showSuccess('Link copied to clipboard!');
                }).catch(() => {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    this.showSuccess('Link copied to clipboard!');
                });
            }

            testLink() {
                if (!this.currentLink) return;
                window.open(this.currentLink.url, '_blank');
            }

            generateQRCode() {
                if (!this.currentLink) return;

                // Redirect to QR generator with the current link
                const qrUrl = `{{ route('qr_generator.create') }}?url=${encodeURIComponent(this.currentLink.url)}`;
                window.open(qrUrl, '_blank');
            }

            shareLink() {
                if (!this.currentLink) return;

                if (navigator.share) {
                    navigator.share({
                        title: 'Generated Link',
                        url: this.currentLink.url
                    });
                } else {
                    // Fallback - copy to clipboard
                    this.copyToClipboard(this.currentLink.url);
                }
            }

            toggleKeyVisibility() {
                const keyField = $('#autologin-key');
                const toggleButton = $('#toggle-key-visibility');
                const icon = toggleButton.find('i');

                if (keyField.attr('type') === 'password') {
                    keyField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    keyField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            }

            previewAutologinLink() {
                const formData = this.getFormData('#autologin-link-generator');
                if (!formData.domain || !formData.key || !formData.email) {
                    this.showError('Please fill in the required fields first.');
                    return;
                }

                const previewUrl = this.buildAutologinUrl(formData);
                this.updateLivePreview(previewUrl);
            }

            updateLivePreview(url = null) {
                const activeTab = $('.nav-link.active').attr('href');

                if (url) {
                    const previewHtml = `
                        <div class="preview-link">
                            <label class="form-label">Preview:</label>
                            <div class="preview-url bg-light p-3 rounded">
                                <small class="text-break">${url}</small>
                            </div>
                            <small class="text-muted mt-2 d-block">
                                <i class="fas fa-info-circle mr-1"></i>
                                This is a preview. Click "Generate" to create the actual link.
                            </small>
                        </div>
                    `;
                    $('#link-preview-container').html(previewHtml);
                } else {
                    $('#link-preview-container').html(`
                        <div class="link-placeholder text-center">
                            <i class="fas fa-link fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Generated link will appear here</p>
                        </div>
                    `);
                }
            }

            saveToHistory(url, type, description) {
                const linkData = {
                    id: Date.now(),
                    url: url,
                    type: type,
                    description: description,
                    created: new Date().toISOString(),
                    clicks: 0
                };

                this.recentLinks.unshift(linkData);

                // Keep only last 10 links
                if (this.recentLinks.length > 10) {
                    this.recentLinks = this.recentLinks.slice(0, 10);
                }

                localStorage.setItem('recentLinks', JSON.stringify(this.recentLinks));
                this.loadRecentLinks();
                this.updateStatistics();
            }

            loadRecentLinks() {
                const container = $('#recent-links-container');

                if (this.recentLinks.length === 0) {
                    container.html(`
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-history fa-2x mb-3"></i>
                            <p>No recent links. Generate your first link to see it here!</p>
                        </div>
                    `);
                    return;
                }

                let html = '<div class="list-group">';

                this.recentLinks.forEach(link => {
                    const date = new Date(link.created).toLocaleDateString();
                    const time = new Date(link.created).toLocaleTimeString();

                    html += `
                        <div class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">${link.type}</h6>
                                <small class="text-muted">${date} ${time}</small>
                            </div>
                            <p class="mb-1 text-break">${link.url}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">${link.description}</small>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary btn-sm" onclick="linkGenerator.copyToClipboard('${link.url}')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="window.open('${link.url}', '_blank')">
                                        <i class="fas fa-external-link-alt"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="linkGenerator.removeFromHistory(${link.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += '</div>';
                container.html(html);
            }

            removeFromHistory(id) {
                this.recentLinks = this.recentLinks.filter(link => link.id !== id);
                localStorage.setItem('recentLinks', JSON.stringify(this.recentLinks));
                this.loadRecentLinks();
                this.updateStatistics();
            }

            clearHistory() {
                Swal.fire({
                    title: 'Clear History?',
                    text: 'This will remove all recent links from your history.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, clear it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        this.recentLinks = [];
                        localStorage.removeItem('recentLinks');
                        this.loadRecentLinks();
                        this.updateStatistics();
                        this.showSuccess('History cleared successfully!');
                    }
                });
            }

            updateStatistics() {
                $('#total-links').text(this.recentLinks.length);
                $('#active-links').text(this.recentLinks.length);

                // Mock statistics for demo
                const totalClicks = this.recentLinks.reduce((sum, link) => sum + link.clicks, 0);
                $('#total-clicks').text(totalClicks);

                const thisMonth = this.recentLinks.filter(link => {
                    const linkDate = new Date(link.created);
                    const now = new Date();
                    return linkDate.getMonth() === now.getMonth() && linkDate.getFullYear() === now.getFullYear();
                }).length;
                $('#month-clicks').text(thisMonth);
            }

            resetAllForms() {
                Swal.fire({
                    title: 'Reset All Forms?',
                    text: 'This will clear all form data.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, reset!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $('.link-form')[0].reset();
                        $('.form-control').removeClass('is-valid is-invalid');
                        this.updateLivePreview();
                        this.resetQuickActions();
                        this.showSuccess('All forms reset successfully!');
                    }
                });
            }

            initLivePreview() {
                this.updateLivePreview();
            }

            showSuccess(message) {
                Swal.fire({
                    title: 'Success!',
                    text: message,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            }

            showError(message) {
                Swal.fire({
                    title: 'Error!',
                    text: message,
                    icon: 'error',
                    confirmButtonColor: '#d33'
                });
            }
        }

        // Initialize Smart Link Generator
        let linkGenerator;
        $(document).ready(function() {
            linkGenerator = new SmartLinkGenerator();
        });
    </script>
@endsection

@section('css')
    <style>
        /* Enhanced Link Generator Styles */
        .bg-gradient-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
        }

        .bg-gradient-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%) !important;
        }

        .bg-gradient-info {
            background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%) !important;
        }

        .bg-gradient-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%) !important;
        }

        .bg-gradient-secondary {
            background: linear-gradient(135deg, var(--gray-500) 0%, var(--gray-600) 100%) !important;
        }

        /* Statistics Cards */
        .card.bg-gradient-primary,
        .card.bg-gradient-success,
        .card.bg-gradient-info,
        .card.bg-gradient-warning {
            border: none !important;
            color: white !important;
            overflow: hidden;
            position: relative;
        }

        .card.bg-gradient-primary::before,
        .card.bg-gradient-success::before,
        .card.bg-gradient-info::before,
        .card.bg-gradient-warning::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        /* Modern Navigation Tabs */
        .nav-tabs-modern {
            border-bottom: 2px solid var(--gray-200);
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
            padding: 0.5rem;
        }

        .nav-tabs-modern .nav-link {
            border: none !important;
            border-radius: var(--radius-md) !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: var(--font-weight-medium) !important;
            color: var(--gray-600) !important;
            transition: all var(--transition-fast) !important;
            margin-right: 0.25rem !important;
            background: transparent !important;
        }

        .nav-tabs-modern .nav-link:hover {
            background-color: var(--gray-100) !important;
            color: var(--gray-700) !important;
            transform: translateY(-1px);
        }

        .nav-tabs-modern .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
            color: #ffffff !important;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3) !important;
        }

        /* Enhanced Form Controls */
        .form-control-modern {
            border: 2px solid var(--border-color) !important;
            border-radius: var(--radius-md) !important;
            padding: 0.875rem 1rem !important;
            font-size: 0.9rem !important;
            transition: all var(--transition-fast) !important;
            background-color: var(--bg-primary) !important;
        }

        .form-control-modern:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
            transform: translateY(-1px);
        }

        .form-control-modern.is-valid {
            border-color: var(--success-color) !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2310b981' d='m2.3 6.73.94-.94 2.94 2.94L8.5 6.4l.94.94L6.5 10.27z'/%3e%3c/svg%3e") !important;
        }

        .form-control-modern.is-invalid {
            border-color: var(--danger-color) !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ef4444'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.5 5.5 1 1m0-1-1 1'/%3e%3c/svg%3e") !important;
        }

        /* Form Labels */
        .form-label {
            font-weight: var(--font-weight-semibold) !important;
            color: var(--text-secondary) !important;
            margin-bottom: 0.5rem !important;
            font-size: 0.875rem !important;
        }

        /* Enhanced Buttons */
        .btn-generate {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
            border: none !important;
            border-radius: var(--radius-lg) !important;
            padding: 0.875rem 2rem !important;
            font-weight: var(--font-weight-semibold) !important;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3) !important;
            transition: all var(--transition-normal) !important;
            color: white !important;
        }

        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4) !important;
            color: white !important;
        }

        /* Link Preview Styles */
        .link-placeholder {
            padding: 2rem;
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--bg-tertiary);
            transition: all var(--transition-fast);
        }

        .link-placeholder:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }

        .generated-link-preview {
            padding: 1rem;
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
        }

        .preview-link {
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }

        .preview-url {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.8rem;
            word-break: break-all;
        }

        /* Input Group Enhancements */
        .input-group-text {
            background: var(--bg-tertiary) !important;
            border: 2px solid var(--border-color) !important;
            color: var(--text-muted) !important;
            font-weight: var(--font-weight-medium) !important;
        }

        /* Advanced Options Card */
        .card.bg-light {
            background: var(--bg-tertiary) !important;
            border: 1px solid var(--border-color) !important;
        }

        .card.bg-light .card-header {
            background: var(--bg-secondary) !important;
            border-bottom: 1px solid var(--border-color) !important;
        }

        .btn-link {
            color: var(--text-secondary) !important;
            text-decoration: none !important;
            font-weight: var(--font-weight-medium) !important;
        }

        .btn-link:hover {
            color: var(--primary-color) !important;
        }

        /* Quick Actions Grid */
        .d-grid {
            display: grid !important;
            gap: 0.5rem;
        }

        .d-grid .btn {
            width: 100%;
            justify-content: flex-start;
        }

        /* Recent Links List */
        .list-group-item {
            border: 1px solid var(--border-color) !important;
            background: var(--bg-primary) !important;
            margin-bottom: 0.5rem !important;
            border-radius: var(--radius-md) !important;
            transition: all var(--transition-fast) !important;
        }

        .list-group-item:hover {
            background: var(--bg-tertiary) !important;
            transform: translateX(4px);
            box-shadow: var(--shadow-sm);
        }

        .list-group-item .btn-group .btn {
            border-radius: var(--radius-sm) !important;
            margin-left: 2px;
        }

        /* Custom Switch Styling */
        .custom-control-input:checked ~ .custom-control-label::before {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
        }

        .custom-control-input:focus ~ .custom-control-label::before {
            box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25) !important;
        }

        .custom-control-label {
            font-weight: var(--font-weight-medium) !important;
            color: var(--text-secondary) !important;
        }

        /* Breadcrumb Enhancement */
        .breadcrumb {
            background: transparent !important;
            padding: 0 !important;
        }

        .breadcrumb-item a {
            color: var(--primary-color) !important;
            text-decoration: none !important;
            font-weight: var(--font-weight-medium) !important;
            transition: all var(--transition-fast) !important;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-hover) !important;
            transform: translateX(2px);
        }

        /* Animation Classes */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        .animate-slide-in-right {
            animation: slideInRight 0.8s ease-out;
        }

        /* Responsive Improvements */
        @media (max-width: 768px) {
            .nav-tabs-modern .nav-link {
                padding: 0.5rem 1rem !important;
                font-size: 0.875rem !important;
            }

            .btn-generate {
                padding: 0.75rem 1.5rem !important;
                font-size: 0.875rem !important;
            }

            .form-control-modern {
                padding: 0.75rem !important;
            }

            .link-placeholder {
                padding: 1.5rem !important;
            }

            .d-grid {
                gap: 0.25rem;
            }

            .list-group-item {
                padding: 0.75rem !important;
            }
        }

        /* Dark Mode Specific Enhancements */
        [data-theme="dark"] .nav-tabs-modern {
            background: var(--bg-tertiary);
        }

        [data-theme="dark"] .nav-tabs-modern .nav-link:hover {
            background-color: var(--bg-secondary) !important;
            color: var(--text-primary) !important;
        }

        [data-theme="dark"] .link-placeholder {
            background: var(--bg-secondary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .link-placeholder:hover {
            background: var(--primary-light);
        }

        [data-theme="dark"] .generated-link-preview {
            background: var(--bg-secondary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .preview-link {
            background: var(--bg-tertiary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .input-group-text {
            background: var(--bg-tertiary) !important;
            color: var(--text-secondary) !important;
        }

        [data-theme="dark"] .card.bg-light {
            background: var(--bg-secondary) !important;
            border-color: var(--border-color) !important;
        }

        [data-theme="dark"] .card.bg-light .card-header {
            background: var(--bg-tertiary) !important;
            border-color: var(--border-color) !important;
        }

        [data-theme="dark"] .btn-link {
            color: var(--text-secondary) !important;
        }

        [data-theme="dark"] .btn-link:hover {
            color: var(--primary-color) !important;
        }
    </style>
@endsection
